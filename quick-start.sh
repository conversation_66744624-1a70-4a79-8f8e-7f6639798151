#!/bin/bash

# VSCodeX Quick Start - All-in-One Script
# نص البدء السريع الشامل لـ VSCodeX

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Functions
print_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                         VSCodeX                              ║"
    echo "║                  نظام Augment Coder محلي                    ║"
    echo "║                   Local Augment Coder System                 ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Main script
main() {
    print_header
    
    echo "مرحباً بك في VSCodeX! سنقوم بإعداد كل شيء لك."
    echo "Welcome to VSCodeX! We'll set up everything for you."
    echo ""
    
    # Step 1: Check system requirements
    print_step "فحص متطلبات النظام..."
    print_step "Checking system requirements..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js غير مثبت"
        print_error "Node.js is not installed"
        echo ""
        echo "يرجى تثبيت Node.js من: https://nodejs.org"
        echo "Please install Node.js from: https://nodejs.org"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "يتطلب Node.js الإصدار 18 أو أحدث"
        print_error "Node.js version 18+ required"
        exit 1
    fi
    
    print_success "Node.js $(node -v) ✓"
    
    # Step 2: Install dependencies
    print_step "تثبيت التبعيات..."
    print_step "Installing dependencies..."
    
    if [ ! -d "node_modules" ]; then
        npm install
    fi
    
    if [ ! -d "backend/node_modules" ]; then
        cd backend && npm install && cd ..
    fi
    
    if [ ! -d "frontend/node_modules" ]; then
        cd frontend && npm install && cd ..
    fi
    
    print_success "تم تثبيت التبعيات ✓"
    print_success "Dependencies installed ✓"
    
    # Step 3: Setup environment
    print_step "إعداد البيئة..."
    print_step "Setting up environment..."
    
    mkdir -p data logs uploads
    
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_success "تم إنشاء ملف .env"
        print_success "Created .env file"
    fi
    
    # Step 4: Check/Install Ollama
    print_step "فحص Ollama..."
    print_step "Checking Ollama..."
    
    if ! command -v ollama &> /dev/null; then
        print_warning "Ollama غير مثبت. تثبيت Ollama..."
        print_warning "Ollama not installed. Installing Ollama..."
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            if command -v brew &> /dev/null; then
                brew install ollama
            else
                curl -fsSL https://ollama.ai/install.sh | sh
            fi
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            curl -fsSL https://ollama.ai/install.sh | sh
        else
            print_error "نظام التشغيل غير مدعوم لتثبيت Ollama التلقائي"
            print_error "Unsupported OS for automatic Ollama installation"
            echo "يرجى تثبيت Ollama يدوياً من: https://ollama.ai"
            echo "Please install Ollama manually from: https://ollama.ai"
            exit 1
        fi
    fi
    
    print_success "Ollama متوفر ✓"
    print_success "Ollama available ✓"
    
    # Step 5: Start Ollama and pull model
    print_step "بدء Ollama وتحميل النموذج..."
    print_step "Starting Ollama and pulling model..."
    
    # Start Ollama in background if not running
    if ! pgrep -f "ollama serve" > /dev/null; then
        ollama serve &
        OLLAMA_PID=$!
        sleep 5
    fi
    
    # Pull model if not exists
    if ! ollama list | grep -q "codellama:7b"; then
        print_info "تحميل نموذج CodeLlama (قد يستغرق بعض الوقت)..."
        print_info "Downloading CodeLlama model (this may take a while)..."
        ollama pull codellama:7b
    fi
    
    print_success "نموذج CodeLlama جاهز ✓"
    print_success "CodeLlama model ready ✓"
    
    # Step 6: Build projects
    print_step "بناء المشاريع..."
    print_step "Building projects..."
    
    cd backend
    npm run build
    cd ..
    
    cd frontend
    npm run build
    cd ..
    
    print_success "تم بناء المشاريع ✓"
    print_success "Projects built ✓"
    
    # Step 7: Run tests
    print_step "تشغيل الاختبارات..."
    print_step "Running tests..."
    
    if ./test.sh; then
        print_success "جميع الاختبارات نجحت ✓"
        print_success "All tests passed ✓"
    else
        print_warning "بعض الاختبارات فشلت، لكن يمكن المتابعة"
        print_warning "Some tests failed, but we can continue"
    fi
    
    # Step 8: Start services
    print_step "بدء الخدمات..."
    print_step "Starting services..."
    
    # Function to cleanup on exit
    cleanup() {
        print_info "إيقاف الخدمات..."
        print_info "Stopping services..."
        
        if [ ! -z "$BACKEND_PID" ]; then
            kill $BACKEND_PID 2>/dev/null || true
        fi
        if [ ! -z "$FRONTEND_PID" ]; then
            kill $FRONTEND_PID 2>/dev/null || true
        fi
        
        print_success "تم إيقاف الخدمات"
        print_success "Services stopped"
        exit 0
    }
    
    trap cleanup SIGINT SIGTERM
    
    # Start backend
    cd backend
    npm start &
    BACKEND_PID=$!
    cd ..
    
    sleep 5
    
    # Start frontend
    cd frontend
    npm start &
    FRONTEND_PID=$!
    cd ..
    
    sleep 5
    
    # Step 9: Success message
    echo ""
    print_success "🎉 VSCodeX جاهز للاستخدام!"
    print_success "🎉 VSCodeX is ready to use!"
    echo ""
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                        معلومات الوصول                        ║"
    echo "║                      Access Information                      ║"
    echo "╠══════════════════════════════════════════════════════════════╣"
    echo "║  🌐 الواجهة الرئيسية: http://localhost:3000                  ║"
    echo "║  🌐 Main Interface: http://localhost:3000                   ║"
    echo "║                                                              ║"
    echo "║  🔌 API الخلفي: http://localhost:3001                        ║"
    echo "║  🔌 Backend API: http://localhost:3001                      ║"
    echo "║                                                              ║"
    echo "║  🤖 Ollama: http://localhost:11434                          ║"
    echo "║                                                              ║"
    echo "║  📊 صحة النظام: http://localhost:3001/health                 ║"
    echo "║  📊 System Health: http://localhost:3001/health             ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    echo "💡 نصائح للاستخدام:"
    echo "💡 Usage Tips:"
    echo "   • ابدأ بإنشاء مشروع جديد أو استيراد مشروع موجود"
    echo "   • Start by creating a new project or importing existing one"
    echo "   • استخدم المساعد الذكي لطرح أسئلة حول الكود"
    echo "   • Use the AI assistant to ask questions about your code"
    echo "   • جميع البيانات محفوظة محلياً على جهازك"
    echo "   • All data is stored locally on your machine"
    echo ""
    echo "🛑 لإيقاف النظام: اضغط Ctrl+C"
    echo "🛑 To stop the system: Press Ctrl+C"
    echo ""
    
    # Open browser (optional)
    if command -v open &> /dev/null; then
        # macOS
        open http://localhost:3000
    elif command -v xdg-open &> /dev/null; then
        # Linux
        xdg-open http://localhost:3000
    elif command -v start &> /dev/null; then
        # Windows
        start http://localhost:3000
    fi
    
    # Wait for user to stop
    wait
}

# Run main function
main "$@"
