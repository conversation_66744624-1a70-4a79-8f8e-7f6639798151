# VSCodeX - نظام Augment Coder محلي

<div align="center">

![VSCodeX Logo](https://via.placeholder.com/200x200/3b82f6/ffffff?text=VSCodeX)

**نظام ذكي للمساعدة في البرمجة يعمل محلياً**
**Local AI-Powered Coding Assistant System**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?logo=react&logoColor=61DAFB)](https://reactjs.org/)

[العربية](#العربية) | [English](#english)

</div>

---

## العربية

### 🚀 نظرة عامة

VSCodeX هو نظام مساعد ذكي للبرمجة يعمل بالكامل على جهازك المحلي. يوفر تجربة مشابهة لـ Augment Coder ولكن مع الحفاظ على خصوصية كودك بنسبة 100%.

### ✨ المميزات الرئيسية

- 🤖 **ذكاء اصطناعي محلي**: مساعد ذكي يعمل بدون إنترنت
- 📁 **فهرسة ذكية**: تحليل وفهرسة تلقائية للكود
- ✏️ **محرر متقدم**: محرر كود بميزات احترافية
- 🔍 **بحث ذكي**: بحث سريع في الملفات والكود
- 💬 **واجهة تفاعلية**: محادثة مع المساعد الذكي
- 🔒 **خصوصية كاملة**: كل شيء يعمل محلياً
- 🌐 **دعم متعدد اللغات**: JavaScript, Python, Java, C++, وأكثر
- 🎨 **واجهة عصرية**: تصميم جميل وسهل الاستخدام

### 🏗️ البنية التقنية

#### Frontend
- **React 18** + **TypeScript** - واجهة المستخدم
- **Next.js 14** - إطار العمل
- **Tailwind CSS** - التصميم
- **Monaco Editor** - محرر الكود
- **Socket.io** - التواصل المباشر
- **Framer Motion** - الحركات والانتقالات

#### Backend
- **Node.js** + **Express** + **TypeScript** - الخادم
- **Socket.io** - التواصل المباشر
- **SQLite** + **TypeORM** - قاعدة البيانات
- **Tree-sitter** - تحليل الكود
- **Chokidar** - مراقبة الملفات

#### AI Engine
- **Ollama** - منصة النماذج المحلية
- **CodeLlama** - نموذج البرمجة الافتراضي
- **CodeGemma** - نموذج بديل
- **Qwen Coder** - نموذج متقدم

### 🚀 البدء السريع

#### الطريقة السهلة (مستحسنة)
```bash
git clone https://github.com/your-username/vscodex.git
cd vscodex
chmod +x quick-start.sh
./quick-start.sh
```

#### التثبيت اليدوي

1. **التحقق من المتطلبات**
```bash
node --version  # يجب أن يكون 18+
npm --version
```

2. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/vscodex.git
cd vscodex
```

3. **تثبيت التبعيات**
```bash
./install.sh
# أو يدوياً:
npm install
cd backend && npm install && cd ..
cd frontend && npm install && cd ..
```

4. **تثبيت Ollama**
```bash
# macOS
brew install ollama

# Linux/WSL
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# تحميل من https://ollama.ai
```

5. **تحميل نموذج AI**
```bash
ollama serve
ollama pull codellama:7b
```

6. **تشغيل النظام**
```bash
./start.sh
# أو يدوياً:
npm run dev
```

7. **فتح المتصفح**
```
http://localhost:3000
```

### 📖 دليل الاستخدام

#### إنشاء مشروع جديد
1. افتح VSCodeX في المتصفح
2. اضغط "مشروع جديد"
3. اختر مجلد المشروع
4. انتظر اكتمال الفهرسة

#### استخدام المساعد الذكي
1. اكتب سؤالك في نافذة المحادثة
2. المساعد سيحلل مشروعك ويقدم إجابة مخصصة
3. يمكنك طلب:
   - شرح الكود
   - إصلاح الأخطاء
   - كتابة كود جديد
   - مراجعة الكود
   - تحسين الأداء

#### تحرير الملفات
1. اختر ملف من مستكشف الملفات
2. استخدم المحرر المتقدم
3. احفظ التغييرات (Ctrl+S)
4. التغييرات تُحفظ تلقائياً

### 🔧 التكوين المتقدم

#### تغيير نموذج AI
```bash
# نماذج مختلفة حسب قوة الجهاز
ollama pull codellama:7b    # افتراضي (4GB RAM)
ollama pull codellama:13b   # متقدم (8GB RAM)
ollama pull codellama:34b   # احترافي (16GB RAM)
ollama pull codegemma:2b    # خفيف (2GB RAM)
```

#### تخصيص الإعدادات
```bash
# تحرير ملف البيئة
nano backend/.env

# الإعدادات المهمة:
OLLAMA_MODEL=codellama:7b
MAX_FILE_SIZE=10485760
LOG_LEVEL=info
```

### 🧪 الاختبار

```bash
# تشغيل جميع الاختبارات
./test.sh

# اختبار Backend فقط
cd backend && npm test

# اختبار Frontend فقط
cd frontend && npm test
```

### 📊 مراقبة الأداء

- **صحة النظام**: http://localhost:3001/health
- **مراقبة الذاكرة**: في شريط الحالة
- **سجلات النظام**: مجلد `logs/`
- **قاعدة البيانات**: مجلد `data/`

### 🐳 Docker

```bash
# تشغيل بـ Docker
docker-compose up -d

# إيقاف الخدمات
docker-compose down
```

### 🤝 المساهمة

نرحب بمساهماتكم! اقرأ [دليل المساهمة](CONTRIBUTING.md) للبدء.

### 📚 الوثائق الإضافية

- [الأسئلة الشائعة](FAQ.md)
- [دليل تحسين الأداء](PERFORMANCE.md)
- [دليل المساهمة](CONTRIBUTING.md)

### 🆘 الحصول على المساعدة

- 📖 راجع [الأسئلة الشائعة](FAQ.md)
- 🐛 أبلغ عن مشكلة في [Issues](https://github.com/your-username/vscodex/issues)
- 💬 انضم للمناقشات في [Discussions](https://github.com/your-username/vscodex/discussions)

### 📄 الترخيص

هذا المشروع مرخص تحت [رخصة MIT](LICENSE).

---

## English

### 🚀 Overview

VSCodeX is a local AI-powered coding assistant that runs entirely on your machine. It provides an Augment Coder-like experience while maintaining 100% privacy of your code.

### ✨ Key Features

- 🤖 **Local AI**: Intelligent assistant that works offline
- 📁 **Smart Indexing**: Automatic code analysis and indexing
- ✏️ **Advanced Editor**: Professional code editor with modern features
- 🔍 **Smart Search**: Fast file and code search
- 💬 **Interactive Interface**: Chat with the AI assistant
- 🔒 **Complete Privacy**: Everything runs locally
- 🌐 **Multi-language Support**: JavaScript, Python, Java, C++, and more
- 🎨 **Modern UI**: Beautiful and user-friendly interface

### 🚀 Quick Start

```bash
git clone https://github.com/your-username/vscodex.git
cd vscodex
chmod +x quick-start.sh
./quick-start.sh
```

### 📖 Documentation

For detailed documentation, please refer to the Arabic section above or visit our [Wiki](https://github.com/your-username/vscodex/wiki).

### 🤝 Contributing

We welcome contributions! Please read our [Contributing Guide](CONTRIBUTING.md) to get started.

### 📄 License

This project is licensed under the [MIT License](LICENSE).

---

<div align="center">

**صنع بـ ❤️ للمطورين العرب**
**Made with ❤️ for Arab Developers**

[⭐ Star this repo](https://github.com/your-username/vscodex) | [🐛 Report Bug](https://github.com/your-username/vscodex/issues) | [💡 Request Feature](https://github.com/your-username/vscodex/issues)

</div>
