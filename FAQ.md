# الأسئلة الشائعة - VSCodeX

## الأسئلة العامة

### ما هو VSCodeX؟
VSCodeX هو نظام مساعد ذكي للبرمجة يعمل محلياً على جهازك. يوفر ميزات مشابهة لـ Augment Coder ولكن مع الحفاظ على خصوصية كودك بالكامل.

### ما الفرق بين VSCodeX و Augment Coder الأصلي؟
- **VSCodeX**: يعمل محلياً، خصوصية كاملة، مجاني ومفتوح المصدر
- **Augment Coder**: يعمل في السحابة، يتطلب اتصال إنترنت، خدمة مدفوعة

### هل يتطلب VSCodeX اتصال إنترنت؟
لا، VSCodeX يعمل بالكامل محلياً على جهازك. تحتاج الإنترنت فقط لتحميل النماذج في البداية.

## التثبيت والإعداد

### ما هي متطلبات النظام؟
- Node.js 18 أو أحدث
- 8GB RAM على الأقل (16GB مفضل)
- 10GB مساحة تخزين فارغة
- نظام التشغيل: Windows, macOS, أو Linux

### كيف أثبت VSCodeX؟
```bash
git clone <repository-url>
cd vscodex
./install.sh
```

### فشل التثبيت، ماذا أفعل؟
1. تأكد من تثبيت Node.js 18+
2. تأكد من وجود مساحة كافية
3. تحقق من اتصال الإنترنت
4. شغل `npm cache clean --force`
5. أعد المحاولة

### كيف أحدث VSCodeX؟
```bash
git pull origin main
npm run install:all
```

## الاستخدام

### كيف أبدأ مشروع جديد؟
1. شغل VSCodeX: `./start.sh`
2. افتح http://localhost:3000
3. اضغط "مشروع جديد"
4. اختر مجلد المشروع

### كيف أستخدم المساعد الذكي؟
1. اختر مشروعك
2. استخدم نافذة المحادثة في الجانب
3. اطرح أسئلتك أو اطلب مساعدة في الكود
4. المساعد سيحلل مشروعك ويقدم إجابات مخصصة

### هل يدعم VSCodeX لغات برمجة متعددة؟
نعم، يدعم:
- JavaScript/TypeScript
- Python
- Java
- C/C++
- C#
- PHP
- Ruby
- Go
- Rust
- Swift
- Kotlin
- HTML/CSS
- وأكثر...

### كيف أحسن دقة المساعد الذكي؟
1. تأكد من فهرسة المشروع بالكامل
2. استخدم أسماء متغيرات وفئات واضحة
3. أضف تعليقات للكود المعقد
4. اطرح أسئلة محددة وواضحة

## المشاكل الشائعة

### المساعد الذكي لا يستجيب
1. تحقق من تشغيل Ollama: `ollama serve`
2. تأكد من تحميل النموذج: `ollama pull codellama:7b`
3. أعد تشغيل Backend
4. تحقق من logs في مجلد `logs/`

### الفهرسة بطيئة جداً
1. استبعد المجلدات الكبيرة (node_modules, .git)
2. قلل حجم الملفات المفهرسة
3. أغلق التطبيقات الأخرى لتوفير ذاكرة
4. استخدم SSD بدلاً من HDD

### استهلاك ذاكرة عالي
1. قلل عدد الملفات المفتوحة
2. أعد تشغيل النظام دورياً
3. استخدم نموذج AI أصغر
4. قلل حجم السياق في المحادثات

### خطأ في الاتصال بقاعدة البيانات
1. تأكد من وجود مجلد `data/`
2. تحقق من صلاحيات الكتابة
3. احذف قاعدة البيانات وأعد الفهرسة
4. أعد تشغيل Backend

## الأداء والتحسين

### كيف أحسن أداء VSCodeX؟
1. استخدم SSD للتخزين
2. أضف المزيد من RAM
3. استبعد الملفات غير الضرورية من الفهرسة
4. استخدم نموذج AI مناسب لجهازك

### أي نموذج AI أختار؟
- **للأجهزة القوية**: `codellama:13b` أو `codellama:34b`
- **للأجهزة المتوسطة**: `codellama:7b` (افتراضي)
- **للأجهزة الضعيفة**: `codegemma:2b`

### كيف أقلل استهلاك الذاكرة؟
1. استخدم نموذج أصغر
2. قلل عدد الملفات المفهرسة
3. أغلق المحرر عند عدم الاستخدام
4. نظف cache دورياً

## الأمان والخصوصية

### هل كودي آمن؟
نعم، كل شيء يعمل محلياً على جهازك. لا يتم إرسال أي كود للخارج.

### هل يتم حفظ محادثاتي؟
نعم، محلياً في قاعدة البيانات على جهازك. يمكنك حذفها في أي وقت.

### كيف أحذف بياناتي؟
```bash
rm -rf data/
rm -rf logs/
```

## التطوير والمساهمة

### كيف أساهم في المشروع؟
1. اقرأ `CONTRIBUTING.md`
2. أنشئ fork للمشروع
3. أضف ميزتك أو إصلاحك
4. أنشئ Pull Request

### كيف أبلغ عن مشكلة؟
1. تأكد من أن المشكلة لم تُبلغ من قبل
2. أنشئ issue جديد
3. أضف تفاصيل كاملة وخطوات إعادة الإنتاج

### كيف أطلب ميزة جديدة؟
1. أنشئ issue مع تسمية "feature request"
2. اشرح الميزة المطلوبة بالتفصيل
3. اذكر حالات الاستخدام

## الدعم الفني

### أين أحصل على المساعدة؟
1. راجع هذا الدليل أولاً
2. ابحث في Issues الموجودة
3. أنشئ issue جديد
4. انضم لمجتمع المطورين

### كيف أحصل على آخر التحديثات؟
1. تابع المشروع على GitHub
2. اشترك في الإشعارات
3. تحقق من Releases دورياً

### هل يوجد دعم تجاري؟
حالياً المشروع مفتوح المصدر ومجاني. للدعم التجاري، تواصل مع فريق التطوير.

---

لم تجد إجابة لسؤالك؟ أنشئ issue جديد وسنساعدك! 🚀
