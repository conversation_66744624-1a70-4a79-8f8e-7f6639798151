#!/bin/bash

# VSCodeX Installation Script
# نص تثبيت VSCodeX

set -e

echo "🚀 بدء تثبيت VSCodeX..."
echo "🚀 Starting VSCodeX installation..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js 18+ أولاً"
    echo "❌ Node.js is not installed. Please install Node.js 18+ first"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ يتطلب Node.js الإصدار 18 أو أحدث. الإصدار الحالي: $(node -v)"
    echo "❌ Node.js version 18+ required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) مثبت"
echo "✅ Node.js $(node -v) is installed"

# Install root dependencies
echo "📦 تثبيت التبعيات الجذرية..."
echo "📦 Installing root dependencies..."
npm install

# Install backend dependencies
echo "📦 تثبيت تبعيات Backend..."
echo "📦 Installing backend dependencies..."
cd backend
npm install
cd ..

# Install frontend dependencies
echo "📦 تثبيت تبعيات Frontend..."
echo "📦 Installing frontend dependencies..."
cd frontend
npm install
cd ..

# Create necessary directories
echo "📁 إنشاء المجلدات المطلوبة..."
echo "📁 Creating necessary directories..."
mkdir -p data logs uploads

# Copy environment file
echo "⚙️ إعداد ملف البيئة..."
echo "⚙️ Setting up environment file..."
if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    echo "✅ تم إنشاء ملف .env من المثال"
    echo "✅ Created .env file from example"
fi

# Check if Ollama is installed
if ! command -v ollama &> /dev/null; then
    echo "🤖 Ollama غير مثبت. تثبيت Ollama..."
    echo "🤖 Ollama is not installed. Installing Ollama..."
    
    # Install Ollama
    curl -fsSL https://ollama.ai/install.sh | sh
    
    echo "✅ تم تثبيت Ollama"
    echo "✅ Ollama installed successfully"
else
    echo "✅ Ollama مثبت مسبقاً"
    echo "✅ Ollama is already installed"
fi

# Start Ollama service
echo "🔄 بدء خدمة Ollama..."
echo "🔄 Starting Ollama service..."
ollama serve &
OLLAMA_PID=$!

# Wait for Ollama to start
sleep 5

# Pull the coding model
echo "📥 تحميل نموذج البرمجة..."
echo "📥 Downloading coding model..."
ollama pull codellama:7b

echo ""
echo "🎉 تم تثبيت VSCodeX بنجاح!"
echo "🎉 VSCodeX installation completed successfully!"
echo ""
echo "لبدء النظام، استخدم الأوامر التالية:"
echo "To start the system, use the following commands:"
echo ""
echo "# تشغيل النظام كاملاً"
echo "# Start the complete system"
echo "npm run dev"
echo ""
echo "# أو تشغيل كل جزء منفصلاً"
echo "# Or start each part separately"
echo "npm run dev:backend    # Backend"
echo "npm run dev:frontend   # Frontend"
echo ""
echo "🌐 ستكون الواجهة متاحة على: http://localhost:3000"
echo "🌐 The interface will be available at: http://localhost:3000"
echo "🔌 API متاح على: http://localhost:3001"
echo "🔌 API available at: http://localhost:3001"
