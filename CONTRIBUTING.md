# دليل المساهمة في VSCodeX

نرحب بمساهماتكم في تطوير VSCodeX! هذا الدليل سيساعدكم على البدء.

## كيفية المساهمة

### 1. إعداد البيئة التطويرية

```bash
# استنساخ المشروع
git clone <repository-url>
cd vscodex

# تثبيت التبعيات
./install.sh

# تشغيل النظام
./start.sh
```

### 2. هيكل المشروع

```
vscodex/
├── backend/          # خادم Node.js/Express
│   ├── src/
│   │   ├── controllers/
│   │   ├── services/
│   │   ├── models/
│   │   ├── routes/
│   │   └── middleware/
│   └── package.json
├── frontend/         # تطبيق Next.js/React
│   ├── src/
│   │   ├── app/
│   │   ├── components/
│   │   ├── services/
│   │   └── hooks/
│   └── package.json
└── docker-compose.yml
```

### 3. معايير الكود

#### Backend (TypeScript/Node.js)
- استخدم TypeScript بدلاً من JavaScript
- اتبع معايير ESLint المحددة
- اكتب تعليقات باللغة العربية للوظائف المهمة
- استخدم async/await بدلاً من Promises

#### Frontend (React/TypeScript)
- استخدم React Hooks
- اتبع معايير Tailwind CSS للتصميم
- استخدم TypeScript للتحقق من الأنواع
- اكتب مكونات قابلة لإعادة الاستخدام

### 4. إرشادات Git

#### رسائل Commit
```
نوع: وصف مختصر

وصف مفصل إذا لزم الأمر

مثال:
feat: إضافة دعم للغة Python في محرك الفهرسة

- إضافة parser للغة Python
- تحسين دقة استخراج الدوال والفئات
- إضافة اختبارات للميزة الجديدة
```

#### أنواع Commit
- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث الوثائق
- `style`: تغييرات التنسيق
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تحديث الاختبارات
- `chore`: مهام صيانة

### 5. عملية المراجعة

1. أنشئ branch جديد للميزة أو الإصلاح
2. اكتب الكود واختبره محلياً
3. أنشئ Pull Request مع وصف واضح
4. انتظر المراجعة والموافقة
5. ادمج التغييرات بعد الموافقة

### 6. الاختبارات

```bash
# تشغيل اختبارات Backend
cd backend
npm test

# تشغيل اختبارات Frontend
cd frontend
npm test
```

### 7. التوثيق

- وثق أي API جديد
- أضف تعليقات للكود المعقد
- حدث README.md عند الحاجة
- اكتب أمثلة للاستخدام

### 8. الإبلاغ عن المشاكل

عند الإبلاغ عن مشكلة، يرجى تضمين:
- وصف واضح للمشكلة
- خطوات إعادة الإنتاج
- البيئة المستخدمة (OS، Node.js version، إلخ)
- رسائل الخطأ إن وجدت
- لقطات شاشة إذا كانت مفيدة

### 9. طلب ميزات جديدة

- اشرح الميزة المطلوبة بوضوح
- اذكر حالات الاستخدام
- اقترح تصميماً أولياً إذا أمكن

### 10. قواعد السلوك

- كن محترماً ومهذباً
- ساعد الآخرين في التعلم
- اقبل النقد البناء
- ركز على تحسين المشروع

## الحصول على المساعدة

إذا كنت بحاجة إلى مساعدة:
- افتح issue جديد
- اطرح سؤالك في المناقشات
- راجع الوثائق الموجودة

شكراً لمساهمتكم في جعل VSCodeX أفضل! 🚀
