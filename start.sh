#!/bin/bash

# VSCodeX Quick Start Script
# نص بدء تشغيل VSCodeX السريع

set -e

echo "🚀 بدء تشغيل VSCodeX..."
echo "🚀 Starting VSCodeX..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js غير مثبت. يرجى تثبيت Node.js 18+ أولاً"
    print_error "Node.js is not installed. Please install Node.js 18+ first"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "يتطلب Node.js الإصدار 18 أو أحدث. الإصدار الحالي: $(node -v)"
    print_error "Node.js version 18+ required. Current version: $(node -v)"
    exit 1
fi

print_success "Node.js $(node -v) متوفر"
print_success "Node.js $(node -v) is available"

# Check if dependencies are installed
if [ ! -d "node_modules" ] || [ ! -d "backend/node_modules" ] || [ ! -d "frontend/node_modules" ]; then
    print_warning "التبعيات غير مثبتة. تشغيل التثبيت..."
    print_warning "Dependencies not installed. Running installation..."
    
    if [ -f "install.sh" ]; then
        chmod +x install.sh
        ./install.sh
    else
        print_status "تثبيت التبعيات يدوياً..."
        print_status "Installing dependencies manually..."
        
        npm install
        cd backend && npm install && cd ..
        cd frontend && npm install && cd ..
    fi
fi

# Check if Ollama is running
print_status "فحص حالة Ollama..."
print_status "Checking Ollama status..."

if ! command -v ollama &> /dev/null; then
    print_warning "Ollama غير مثبت. يرجى تثبيت Ollama أولاً:"
    print_warning "Ollama is not installed. Please install Ollama first:"
    echo "curl -fsSL https://ollama.ai/install.sh | sh"
    echo "ollama pull codellama:7b"
    exit 1
fi

# Start Ollama if not running
if ! pgrep -f "ollama serve" > /dev/null; then
    print_status "بدء خدمة Ollama..."
    print_status "Starting Ollama service..."
    ollama serve &
    OLLAMA_PID=$!
    sleep 3
    
    # Check if model is available
    if ! ollama list | grep -q "codellama"; then
        print_status "تحميل نموذج CodeLlama..."
        print_status "Downloading CodeLlama model..."
        ollama pull codellama:7b
    fi
else
    print_success "Ollama يعمل بالفعل"
    print_success "Ollama is already running"
fi

# Create necessary directories
mkdir -p data logs uploads

# Copy environment file if it doesn't exist
if [ ! -f "backend/.env" ]; then
    if [ -f "backend/.env.example" ]; then
        cp backend/.env.example backend/.env
        print_success "تم إنشاء ملف .env"
        print_success "Created .env file"
    fi
fi

# Function to cleanup on exit
cleanup() {
    print_status "إيقاف الخدمات..."
    print_status "Stopping services..."
    
    # Kill background processes
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$OLLAMA_PID" ]; then
        kill $OLLAMA_PID 2>/dev/null || true
    fi
    
    print_success "تم إيقاف جميع الخدمات"
    print_success "All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start backend
print_status "بدء Backend..."
print_status "Starting Backend..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

# Wait a bit for backend to start
sleep 5

# Start frontend
print_status "بدء Frontend..."
print_status "Starting Frontend..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

# Wait a bit for frontend to start
sleep 5

print_success "🎉 تم تشغيل VSCodeX بنجاح!"
print_success "🎉 VSCodeX started successfully!"
echo ""
echo "🌐 الواجهة متاحة على: http://localhost:3000"
echo "🌐 Frontend available at: http://localhost:3000"
echo "🔌 API متاح على: http://localhost:3001"
echo "🔌 API available at: http://localhost:3001"
echo "🤖 Ollama متاح على: http://localhost:11434"
echo "🤖 Ollama available at: http://localhost:11434"
echo ""
echo "اضغط Ctrl+C لإيقاف النظام"
echo "Press Ctrl+C to stop the system"

# Wait for user to stop
wait
