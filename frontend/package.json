{"name": "vscodex-frontend", "version": "1.0.0", "description": "Frontend for VSCodeX - Local Augment Coder System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.5", "@monaco-editor/react": "^4.6.0", "monaco-editor": "^0.45.0", "axios": "^1.6.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "framer-motion": "^10.16.16", "react-resizable-panels": "^0.0.55", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "highlight.js": "^11.9.0", "date-fns": "^3.0.6", "react-hook-form": "^7.48.2", "react-dropzone": "^14.2.3"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/forms": "^0.5.7"}}