'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FiWifi, 
  FiWifiOff, 
  FiActivity, 
  FiCpu, 
  FiHardDrive,
  FiGitBranch,
  FiClock
} from 'react-icons/fi';
import { useQuery } from 'react-query';
import { projectService } from '@/services/projectService';

interface StatusBarProps {
  isConnected: boolean;
  projectId: string;
  selectedFile: string | null;
}

export function StatusBar({ isConnected, projectId, selectedFile }: StatusBarProps) {
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Get project stats
  const { data: projectStats } = useQuery(
    ['projectStats', projectId],
    () => projectService.getProjectStats(projectId),
    {
      enabled: !!projectId,
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  );

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="h-6 bg-primary-600 text-white text-xs flex items-center justify-between px-4"
    >
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        {/* Connection Status */}
        <div className="flex items-center space-x-1">
          {isConnected ? (
            <>
              <FiWifi className="w-3 h-3 text-green-300" />
              <span className="text-green-300">متصل</span>
            </>
          ) : (
            <>
              <FiWifiOff className="w-3 h-3 text-red-300" />
              <span className="text-red-300">غير متصل</span>
            </>
          )}
        </div>

        {/* Project Stats */}
        {projectStats && (
          <>
            <div className="flex items-center space-x-1">
              <FiHardDrive className="w-3 h-3" />
              <span>{projectStats.totalFiles} ملف</span>
            </div>
            
            <div className="flex items-center space-x-1">
              <FiActivity className="w-3 h-3" />
              <span>{projectStats.totalLines.toLocaleString()} سطر</span>
            </div>
          </>
        )}

        {/* Current File Info */}
        {selectedFile && (
          <div className="flex items-center space-x-1">
            <span className="text-primary-200">📄</span>
            <span>{selectedFile.split('/').pop()}</span>
          </div>
        )}
      </div>

      {/* Center Section */}
      <div className="flex items-center space-x-4">
        {/* Indexing Progress */}
        {projectStats?.indexingProgress && (
          <div className="flex items-center space-x-2">
            <div className="w-16 h-1 bg-primary-800 rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-green-400"
                initial={{ width: 0 }}
                animate={{ 
                  width: `${(projectStats.indexingProgress.completed / projectStats.indexingProgress.total) * 100}%` 
                }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <span className="text-xs">
              فهرسة {projectStats.indexingProgress.completed}/{projectStats.indexingProgress.total}
            </span>
          </div>
        )}

        {/* Language Breakdown */}
        {projectStats?.languageBreakdown && (
          <div className="flex items-center space-x-2">
            {Object.entries(projectStats.languageBreakdown)
              .slice(0, 3)
              .map(([lang, count]) => (
                <span key={lang} className="text-primary-200">
                  {lang}: {count}
                </span>
              ))}
          </div>
        )}
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-4">
        {/* Git Branch (if available) */}
        <div className="flex items-center space-x-1">
          <FiGitBranch className="w-3 h-3" />
          <span>main</span>
        </div>

        {/* System Status */}
        <div className="flex items-center space-x-1">
          <FiCpu className="w-3 h-3" />
          <span>AI جاهز</span>
        </div>

        {/* Current Time */}
        <div className="flex items-center space-x-1">
          <FiClock className="w-3 h-3" />
          <span>{formatTime(currentTime)}</span>
        </div>
      </div>
    </motion.div>
  );
}
