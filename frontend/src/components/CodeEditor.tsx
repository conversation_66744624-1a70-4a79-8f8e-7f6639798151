'use client';

import { useState, useEffect, useRef } from 'react';
import { Editor } from '@monaco-editor/react';
import { motion } from 'framer-motion';
import { FiSave, FiCode, FiSettings, FiMaximize2, FiMinimize2 } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { fileService } from '@/services/fileService';
import toast from 'react-hot-toast';

interface CodeEditorProps {
  file: string | null;
  projectId: string;
}

export function CodeEditor({ file, projectId }: CodeEditorProps) {
  const [content, setContent] = useState('');
  const [isModified, setIsModified] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [editorSettings, setEditorSettings] = useState({
    theme: 'vs-dark',
    fontSize: 14,
    wordWrap: 'on' as const,
    minimap: { enabled: true },
    lineNumbers: 'on' as const,
  });

  const editorRef = useRef<any>(null);
  const queryClient = useQueryClient();

  // Fetch file content
  const { data: fileContent, isLoading } = useQuery(
    ['fileContent', projectId, file],
    () => file ? fileService.getFileContentByPath(projectId, file) : null,
    {
      enabled: !!file && !!projectId,
      onSuccess: (data) => {
        if (data) {
          setContent(data.content);
          setIsModified(false);
        }
      },
      onError: (error) => {
        toast.error('فشل في تحميل الملف');
        console.error('Error loading file:', error);
      }
    }
  );

  // Save file mutation
  const saveMutation = useMutation(
    (newContent: string) => 
      fileService.updateFileByPath(projectId, file!, newContent),
    {
      onSuccess: () => {
        setIsModified(false);
        toast.success('تم حفظ الملف بنجاح');
        queryClient.invalidateQueries(['fileContent', projectId, file]);
      },
      onError: (error) => {
        toast.error('فشل في حفظ الملف');
        console.error('Error saving file:', error);
      }
    }
  );

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        handleSave();
      }
      if (e.key === 'F11') {
        e.preventDefault();
        setIsFullscreen(!isFullscreen);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreen, content, file]);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // Configure editor
    editor.updateOptions({
      automaticLayout: true,
      scrollBeyondLastLine: false,
      renderWhitespace: 'selection',
      rulers: [80, 120],
    });

    // Add custom commands
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    // Configure language features
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.Latest,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types']
    });
  };

  const handleContentChange = (value: string | undefined) => {
    if (value !== undefined) {
      setContent(value);
      setIsModified(value !== fileContent?.content);
    }
  };

  const handleSave = () => {
    if (file && isModified) {
      saveMutation.mutate(content);
    }
  };

  const getLanguage = (filePath: string): string => {
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'h': 'c',
      'hpp': 'cpp',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'swift': 'swift',
      'kt': 'kotlin',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
      'sql': 'sql',
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',
      'ps1': 'powershell',
      'dockerfile': 'dockerfile',
      'gitignore': 'ignore',
    };
    return languageMap[extension || ''] || 'plaintext';
  };

  const formatDocument = () => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument').run();
    }
  };

  const toggleTheme = () => {
    const newTheme = editorSettings.theme === 'vs-dark' ? 'vs-light' : 'vs-dark';
    setEditorSettings(prev => ({ ...prev, theme: newTheme }));
  };

  if (!file) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <FiCode className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            لم يتم اختيار ملف
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            اختر ملفاً من مستكشف الملفات لبدء التحرير
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-4 border-primary-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">جاري تحميل الملف...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`h-full flex flex-col bg-white dark:bg-gray-900 ${
      isFullscreen ? 'fixed inset-0 z-50' : ''
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center">
          <FiCode className="w-5 h-5 text-primary-500 mr-2" />
          <span className="font-medium text-gray-900 dark:text-white">
            {file.split('/').pop()}
          </span>
          {isModified && (
            <span className="ml-2 w-2 h-2 bg-orange-500 rounded-full" title="ملف معدل" />
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={formatDocument}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            title="تنسيق الكود"
          >
            <FiSettings className="w-4 h-4" />
          </button>
          
          <button
            onClick={handleSave}
            disabled={!isModified || saveMutation.isLoading}
            className="flex items-center px-3 py-1.5 bg-primary-500 text-white rounded hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
          >
            <FiSave className="w-4 h-4 mr-1" />
            {saveMutation.isLoading ? 'جاري الحفظ...' : 'حفظ'}
          </button>

          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            title={isFullscreen ? 'تصغير' : 'ملء الشاشة'}
          >
            {isFullscreen ? (
              <FiMinimize2 className="w-4 h-4" />
            ) : (
              <FiMaximize2 className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1">
        <Editor
          height="100%"
          language={getLanguage(file)}
          value={content}
          onChange={handleContentChange}
          onMount={handleEditorDidMount}
          theme={editorSettings.theme}
          options={{
            fontSize: editorSettings.fontSize,
            wordWrap: editorSettings.wordWrap,
            minimap: editorSettings.minimap,
            lineNumbers: editorSettings.lineNumbers,
            automaticLayout: true,
            scrollBeyondLastLine: false,
            renderWhitespace: 'selection',
            rulers: [80, 120],
            bracketPairColorization: { enabled: true },
            guides: {
              bracketPairs: true,
              indentation: true,
            },
            suggest: {
              showKeywords: true,
              showSnippets: true,
            },
            quickSuggestions: {
              other: true,
              comments: true,
              strings: true,
            },
            parameterHints: { enabled: true },
            hover: { enabled: true },
            contextmenu: true,
            mouseWheelZoom: true,
          }}
        />
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-3 py-1 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs">
        <div className="flex items-center space-x-4">
          <span className="text-gray-600 dark:text-gray-400">
            {getLanguage(file).toUpperCase()}
          </span>
          {fileContent && (
            <>
              <span className="text-gray-600 dark:text-gray-400">
                {fileContent.lines} سطر
              </span>
              <span className="text-gray-600 dark:text-gray-400">
                {Math.round(fileContent.size / 1024)} KB
              </span>
            </>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {isModified && (
            <span className="text-orange-600 dark:text-orange-400">
              معدل
            </span>
          )}
          <button
            onClick={toggleTheme}
            className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          >
            {editorSettings.theme === 'vs-dark' ? '🌙' : '☀️'}
          </button>
        </div>
      </div>
    </div>
  );
}
