'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiFolder, 
  FiFolderOpen, 
  FiFile, 
  FiChevronRight, 
  FiChevronDown,
  FiRefreshCw,
  FiSearch
} from 'react-icons/fi';
import { useQuery } from 'react-query';
import { fileService } from '@/services/fileService';

interface FileNode {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileNode[];
  size?: number;
  extension?: string;
  language?: string;
}

interface FileExplorerProps {
  projectId: string;
  onFileSelect: (filePath: string) => void;
  selectedFile: string | null;
}

export function FileExplorer({ projectId, onFileSelect, selectedFile }: FileExplorerProps) {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredFiles, setFilteredFiles] = useState<FileNode[]>([]);

  const { data: fileTree, isLoading, refetch } = useQuery(
    ['fileTree', projectId],
    () => fileService.getFileTree(projectId),
    {
      enabled: !!projectId,
      staleTime: 30000, // 30 seconds
    }
  );

  useEffect(() => {
    if (fileTree && searchQuery) {
      const filtered = filterFiles(fileTree, searchQuery.toLowerCase());
      setFilteredFiles(filtered);
    } else {
      setFilteredFiles(fileTree || []);
    }
  }, [fileTree, searchQuery]);

  const filterFiles = (files: FileNode[], query: string): FileNode[] => {
    return files.reduce((acc: FileNode[], file) => {
      if (file.name.toLowerCase().includes(query)) {
        acc.push(file);
      } else if (file.children) {
        const filteredChildren = filterFiles(file.children, query);
        if (filteredChildren.length > 0) {
          acc.push({
            ...file,
            children: filteredChildren
          });
        }
      }
      return acc;
    }, []);
  };

  const toggleFolder = (path: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedFolders(newExpanded);
  };

  const getFileIcon = (file: FileNode) => {
    if (file.type === 'directory') {
      return expandedFolders.has(file.path) ? FiFolderOpen : FiFolder;
    }
    return FiFile;
  };

  const getFileColor = (file: FileNode) => {
    if (file.type === 'directory') {
      return 'text-blue-500 dark:text-blue-400';
    }
    
    switch (file.extension) {
      case '.js':
      case '.jsx':
        return 'text-yellow-500';
      case '.ts':
      case '.tsx':
        return 'text-blue-600';
      case '.py':
        return 'text-green-500';
      case '.java':
        return 'text-red-500';
      case '.cpp':
      case '.c':
      case '.h':
        return 'text-purple-500';
      case '.css':
      case '.scss':
        return 'text-pink-500';
      case '.html':
        return 'text-orange-500';
      case '.json':
        return 'text-gray-500';
      case '.md':
        return 'text-gray-600';
      default:
        return 'text-gray-400';
    }
  };

  const renderFileNode = (file: FileNode, depth: number = 0) => {
    const Icon = getFileIcon(file);
    const isExpanded = expandedFolders.has(file.path);
    const isSelected = selectedFile === file.path;

    return (
      <div key={file.path}>
        <motion.div
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          className={`flex items-center py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer rounded ${
            isSelected ? 'bg-primary-100 dark:bg-primary-900' : ''
          }`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => {
            if (file.type === 'directory') {
              toggleFolder(file.path);
            } else {
              onFileSelect(file.path);
            }
          }}
        >
          {file.type === 'directory' && (
            <motion.div
              animate={{ rotate: isExpanded ? 90 : 0 }}
              transition={{ duration: 0.2 }}
              className="mr-1"
            >
              <FiChevronRight className="w-3 h-3 text-gray-400" />
            </motion.div>
          )}
          <Icon className={`w-4 h-4 mr-2 ${getFileColor(file)}`} />
          <span className={`text-sm truncate ${
            isSelected 
              ? 'text-primary-700 dark:text-primary-300 font-medium' 
              : 'text-gray-700 dark:text-gray-300'
          }`}>
            {file.name}
          </span>
          {file.type === 'file' && file.size && (
            <span className="text-xs text-gray-400 ml-auto">
              {formatFileSize(file.size)}
            </span>
          )}
        </motion.div>

        <AnimatePresence>
          {file.type === 'directory' && isExpanded && file.children && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
            >
              {file.children.map(child => renderFileNode(child, depth + 1))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-2">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded flex-1"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-900 dark:text-white">
            مستكشف الملفات
          </h3>
          <button
            onClick={() => refetch()}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            title="تحديث"
          >
            <FiRefreshCw className="w-4 h-4" />
          </button>
        </div>

        {/* Search */}
        <div className="relative">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="البحث في الملفات..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
          />
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto p-2">
        {filteredFiles.length > 0 ? (
          <div className="space-y-1">
            {filteredFiles.map(file => renderFileNode(file))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiFolder className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              {searchQuery ? 'لم يتم العثور على ملفات' : 'لا توجد ملفات'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
