'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FiFolderOpen, FiPlus, FiRefreshCw } from 'react-icons/fi';
import { useQuery } from 'react-query';
import { projectService } from '@/services/projectService';
import toast from 'react-hot-toast';

interface ProjectSelectorProps {
  onProjectSelect: (projectId: string) => void;
}

export function ProjectSelector({ onProjectSelect }: ProjectSelectorProps) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newProjectPath, setNewProjectPath] = useState('');
  const [newProjectName, setNewProjectName] = useState('');

  const { data: projects, isLoading, refetch } = useQuery(
    'projects',
    projectService.getAllProjects,
    {
      onError: (error) => {
        toast.error('فشل في تحميل المشاريع');
        console.error('Error loading projects:', error);
      }
    }
  );

  const handleCreateProject = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newProjectPath.trim() || !newProjectName.trim()) {
      toast.error('يرجى ملء جميع الحقول');
      return;
    }

    try {
      const project = await projectService.createProject({
        name: newProjectName,
        path: newProjectPath,
        description: `مشروع ${newProjectName}`
      });

      toast.success('تم إنشاء المشروع بنجاح');
      setShowCreateForm(false);
      setNewProjectPath('');
      setNewProjectName('');
      refetch();
      onProjectSelect(project.id);
    } catch (error) {
      toast.error('فشل في إنشاء المشروع');
      console.error('Error creating project:', error);
    }
  };

  const handleSelectFolder = async () => {
    try {
      // This would use the File System Access API in modern browsers
      // For now, we'll use a simple input
      const input = document.createElement('input');
      input.type = 'file';
      input.webkitdirectory = true;
      input.onchange = (e) => {
        const files = (e.target as HTMLInputElement).files;
        if (files && files.length > 0) {
          const path = files[0].webkitRelativePath.split('/')[0];
          setNewProjectPath(path);
          setNewProjectName(path);
        }
      };
      input.click();
    } catch (error) {
      toast.error('فشل في اختيار المجلد');
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-4 border-primary-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">جاري تحميل المشاريع...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          اختر مشروعاً
        </h2>
        <div className="flex gap-2">
          <button
            onClick={() => refetch()}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            title="تحديث"
          >
            <FiRefreshCw className="w-5 h-5" />
          </button>
          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            مشروع جديد
          </button>
        </div>
      </div>

      {showCreateForm && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <form onSubmit={handleCreateProject} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                اسم المشروع
              </label>
              <input
                type="text"
                value={newProjectName}
                onChange={(e) => setNewProjectName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                placeholder="اسم المشروع"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                مسار المشروع
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newProjectPath}
                  onChange={(e) => setNewProjectPath(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="/path/to/project"
                  required
                />
                <button
                  type="button"
                  onClick={handleSelectFolder}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                >
                  <FiFolderOpen className="w-4 h-4" />
                </button>
              </div>
            </div>
            <div className="flex gap-2">
              <button
                type="submit"
                className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
              >
                إنشاء
              </button>
              <button
                type="button"
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </form>
        </motion.div>
      )}

      {projects && projects.length > 0 ? (
        <div className="grid gap-4">
          {projects.map((project) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => onProjectSelect(project.id)}
              className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-primary-500 dark:hover:border-primary-400 cursor-pointer transition-colors group"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <div className="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg mr-3 group-hover:bg-primary-200 dark:group-hover:bg-primary-800 transition-colors">
                    <FiFolderOpen className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {project.name}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {project.path}
                    </p>
                    {project.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        {project.description}
                      </p>
                    )}
                  </div>
                </div>
                <div className="text-xs text-gray-400 dark:text-gray-500">
                  {project.fileCount} ملف
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FiFolderOpen className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            لا توجد مشاريع
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            ابدأ بإنشاء مشروع جديد أو استيراد مشروع موجود
          </p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            إنشاء مشروع جديد
          </button>
        </div>
      )}
    </div>
  );
}
