import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export interface Project {
  id: string;
  name: string;
  path: string;
  description?: string;
  status: 'active' | 'archived' | 'deleted';
  settings?: {
    excludePatterns?: string[];
    includePatterns?: string[];
    maxFileSize?: number;
    autoIndex?: boolean;
  };
  lastIndexed?: string;
  fileCount: number;
  totalLines: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProjectRequest {
  name: string;
  path: string;
  description?: string;
  settings?: Project['settings'];
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
  settings?: Project['settings'];
  status?: Project['status'];
}

export interface ProjectStats {
  totalFiles: number;
  totalLines: number;
  languageBreakdown: Record<string, number>;
  fileTypeBreakdown: Record<string, number>;
  lastModified: string;
  indexingProgress?: {
    total: number;
    completed: number;
    status: string;
  };
}

export const projectService = {
  // Get all projects
  async getAllProjects(): Promise<Project[]> {
    const response = await api.get('/projects');
    return response.data;
  },

  // Get project by ID
  async getProjectById(id: string): Promise<Project> {
    const response = await api.get(`/projects/${id}`);
    return response.data;
  },

  // Create new project
  async createProject(data: CreateProjectRequest): Promise<Project> {
    const response = await api.post('/projects', data);
    return response.data;
  },

  // Update project
  async updateProject(id: string, data: UpdateProjectRequest): Promise<Project> {
    const response = await api.put(`/projects/${id}`, data);
    return response.data;
  },

  // Delete project
  async deleteProject(id: string): Promise<void> {
    await api.delete(`/projects/${id}`);
  },

  // Index project files
  async indexProject(id: string): Promise<{ message: string; jobId: string }> {
    const response = await api.post(`/projects/${id}/index`);
    return response.data;
  },

  // Get project statistics
  async getProjectStats(id: string): Promise<ProjectStats> {
    const response = await api.get(`/projects/${id}/stats`);
    return response.data;
  },

  // Archive project
  async archiveProject(id: string): Promise<Project> {
    return this.updateProject(id, { status: 'archived' });
  },

  // Restore project
  async restoreProject(id: string): Promise<Project> {
    return this.updateProject(id, { status: 'active' });
  },
};
