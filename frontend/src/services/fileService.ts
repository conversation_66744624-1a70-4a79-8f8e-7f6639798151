import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface FileNode {
  id: string;
  name: string;
  path: string;
  relativePath: string;
  type: 'file' | 'directory';
  extension?: string;
  language?: string;
  size?: number;
  lines?: number;
  lastModified?: string;
  children?: FileNode[];
}

export interface FileContent {
  id: string;
  name: string;
  path: string;
  content: string;
  language?: string;
  size: number;
  lines: number;
  lastModified: string;
  isReadonly?: boolean;
}

export interface CreateFileRequest {
  name: string;
  path: string;
  content?: string;
  projectId: string;
}

export interface UpdateFileRequest {
  content: string;
}

export const fileService = {
  // Get file tree for project
  async getFileTree(projectId: string): Promise<FileNode[]> {
    const response = await api.get(`/files/tree/${projectId}`);
    return response.data;
  },

  // Get file content
  async getFileContent(fileId: string): Promise<FileContent> {
    const response = await api.get(`/files/${fileId}/content`);
    return response.data;
  },

  // Get file content by path
  async getFileContentByPath(projectId: string, filePath: string): Promise<FileContent> {
    const response = await api.get(`/files/content`, {
      params: { projectId, path: filePath }
    });
    return response.data;
  },

  // Create new file
  async createFile(data: CreateFileRequest): Promise<FileNode> {
    const response = await api.post('/files', data);
    return response.data;
  },

  // Update file content
  async updateFile(fileId: string, data: UpdateFileRequest): Promise<FileContent> {
    const response = await api.put(`/files/${fileId}`, data);
    return response.data;
  },

  // Update file content by path
  async updateFileByPath(projectId: string, filePath: string, content: string): Promise<FileContent> {
    const response = await api.put('/files/content', {
      projectId,
      path: filePath,
      content
    });
    return response.data;
  },

  // Delete file
  async deleteFile(fileId: string): Promise<void> {
    await api.delete(`/files/${fileId}`);
  },

  // Delete file by path
  async deleteFileByPath(projectId: string, filePath: string): Promise<void> {
    await api.delete('/files/content', {
      params: { projectId, path: filePath }
    });
  },

  // Search files
  async searchFiles(projectId: string, query: string): Promise<FileNode[]> {
    const response = await api.get(`/files/search`, {
      params: { projectId, q: query }
    });
    return response.data;
  },

  // Search in file content
  async searchInFiles(projectId: string, query: string): Promise<Array<{
    file: FileNode;
    matches: Array<{
      line: number;
      content: string;
      startColumn: number;
      endColumn: number;
    }>;
  }>> {
    const response = await api.get(`/files/search-content`, {
      params: { projectId, q: query }
    });
    return response.data;
  },

  // Get file statistics
  async getFileStats(fileId: string): Promise<{
    size: number;
    lines: number;
    characters: number;
    words: number;
    language: string;
    encoding: string;
  }> {
    const response = await api.get(`/files/${fileId}/stats`);
    return response.data;
  },

  // Get recent files
  async getRecentFiles(projectId: string, limit: number = 10): Promise<FileNode[]> {
    const response = await api.get(`/files/recent`, {
      params: { projectId, limit }
    });
    return response.data;
  },

  // Rename file
  async renameFile(fileId: string, newName: string): Promise<FileNode> {
    const response = await api.patch(`/files/${fileId}/rename`, { name: newName });
    return response.data;
  },

  // Move file
  async moveFile(fileId: string, newPath: string): Promise<FileNode> {
    const response = await api.patch(`/files/${fileId}/move`, { path: newPath });
    return response.data;
  },

  // Copy file
  async copyFile(fileId: string, newPath: string): Promise<FileNode> {
    const response = await api.post(`/files/${fileId}/copy`, { path: newPath });
    return response.data;
  },

  // Get file history (if version control is available)
  async getFileHistory(fileId: string): Promise<Array<{
    id: string;
    message: string;
    author: string;
    date: string;
    hash: string;
  }>> {
    const response = await api.get(`/files/${fileId}/history`);
    return response.data;
  },

  // Format file content
  async formatFile(fileId: string): Promise<{ content: string }> {
    const response = await api.post(`/files/${fileId}/format`);
    return response.data;
  },

  // Validate file syntax
  async validateFile(fileId: string): Promise<Array<{
    line: number;
    column: number;
    message: string;
    severity: 'error' | 'warning' | 'info';
  }>> {
    const response = await api.post(`/files/${fileId}/validate`);
    return response.data;
  },
};
