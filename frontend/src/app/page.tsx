'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FiCode, FiMessageSquare, FiFolderOpen, FiSettings, FiPlay, FiDownload } from 'react-icons/fi';
import { ProjectSelector } from '@/components/ProjectSelector';
import { ChatInterface } from '@/components/ChatInterface';
import { FileExplorer } from '@/components/FileExplorer';
import { CodeEditor } from '@/components/CodeEditor';
import { StatusBar } from '@/components/StatusBar';

export default function HomePage() {
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Check backend connection
    const checkConnection = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/health`);
        setIsConnected(response.ok);
      } catch (error) {
        setIsConnected(false);
      }
    };

    checkConnection();
    const interval = setInterval(checkConnection, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  if (!selectedProject) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <div className="flex items-center justify-center mb-6">
              <div className="bg-primary-500 p-3 rounded-xl shadow-lg">
                <FiCode className="w-8 h-8 text-white" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              مرحباً بك في VSCodeX
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              مساعد ذكي للبرمجة يعمل محلياً مع الذكاء الاصطناعي
            </p>
            
            {/* Connection Status */}
            <div className="mt-6 flex items-center justify-center">
              <div className={`flex items-center px-4 py-2 rounded-full ${
                isConnected 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`} />
                {isConnected ? 'متصل بالخادم' : 'غير متصل بالخادم'}
              </div>
            </div>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
          >
            {[
              {
                icon: FiCode,
                title: 'تحرير الكود',
                description: 'محرر متقدم مع دعم لغات متعددة'
              },
              {
                icon: FiMessageSquare,
                title: 'مساعد ذكي',
                description: 'ذكاء اصطناعي محلي للمساعدة في البرمجة'
              },
              {
                icon: FiFolderOpen,
                title: 'إدارة المشاريع',
                description: 'تنظيم وفهرسة مشاريعك بذكاء'
              },
              {
                icon: FiSettings,
                title: 'خصوصية كاملة',
                description: 'كل شيء يعمل محلياً على جهازك'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
              >
                <feature.icon className="w-8 h-8 text-primary-500 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </motion.div>

          {/* Project Selector */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="max-w-2xl mx-auto"
          >
            <ProjectSelector onProjectSelect={setSelectedProject} />
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="mt-12 text-center"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
              إجراءات سريعة
            </h3>
            <div className="flex flex-wrap justify-center gap-4">
              <button className="flex items-center px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">
                <FiPlay className="w-4 h-4 mr-2" />
                بدء مشروع جديد
              </button>
              <button className="flex items-center px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                <FiDownload className="w-4 h-4 mr-2" />
                تحميل نموذج AI
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Main Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
          {/* File Explorer */}
          <div className="flex-1 overflow-hidden">
            <FileExplorer 
              projectId={selectedProject} 
              onFileSelect={setSelectedFile}
              selectedFile={selectedFile}
            />
          </div>
          
          {/* Chat Interface */}
          <div className="h-96 border-t border-gray-200 dark:border-gray-700">
            <ChatInterface projectId={selectedProject} />
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Code Editor */}
          <div className="flex-1">
            <CodeEditor 
              file={selectedFile}
              projectId={selectedProject}
            />
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <StatusBar 
        isConnected={isConnected}
        projectId={selectedProject}
        selectedFile={selectedFile}
      />
    </div>
  );
}
