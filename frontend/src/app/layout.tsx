import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'VSCodeX - نظام Augment Coder محلي',
  description: 'مساعد ذكي للبرمجة يعمل محلياً مع الذكاء الاصطناعي',
  keywords: ['AI', 'Coding', 'Assistant', 'Local', 'Arabic', 'Programming'],
  authors: [{ name: 'VSCodeX Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#3b82f6',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${inter.className} antialiased`}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
