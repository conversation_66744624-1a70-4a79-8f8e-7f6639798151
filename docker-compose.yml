version: '3.8'

services:
  # Ollama Service for AI Models
  ollama:
    image: ollama/ollama:latest
    container_name: vscodex-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    networks:
      - vscodex-network

  # Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: vscodex-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DB_PATH=/app/data/vscodex.db
      - OLLAMA_HOST=http://ollama:11434
      - OLLAMA_MODEL=codellama:7b
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      - ollama
    restart: unless-stopped
    networks:
      - vscodex-network

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: vscodex-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3001
      - NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - vscodex-network

volumes:
  ollama_data:
    driver: local

networks:
  vscodex-network:
    driver: bridge
