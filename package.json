{"name": "vscodex", "version": "1.0.0", "description": "نظام Augment Coder محلي - مساعد ذكي للبرمجة", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules backend/dist frontend/dist"}, "keywords": ["ai", "coding-assistant", "local", "augment", "coder", "programming", "arabic"], "author": "VSCodeX Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}}