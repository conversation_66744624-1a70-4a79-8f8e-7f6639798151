# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_PATH=./data/vscodex.db

# AI Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=codellama:7b

# Security
JWT_SECRET=your-super-secret-jwt-key-here
BCRYPT_ROUNDS=12

# File System
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=.js,.ts,.jsx,.tsx,.py,.java,.cpp,.c,.h,.hpp,.rs,.go,.php,.rb,.swift,.kt,.cs,.html,.css,.scss,.sass,.less,.json,.xml,.yaml,.yml,.md,.txt

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS
CORS_ORIGIN=http://localhost:3000

# Socket.IO
SOCKET_CORS_ORIGIN=http://localhost:3000
