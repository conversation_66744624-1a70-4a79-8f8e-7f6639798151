import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, Index } from 'typeorm';
import { Project } from './Project';
import { CodeIndex } from './CodeIndex';

@Entity('files')
export class File {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 500 })
  @Index()
  name: string;

  @Column({ type: 'text' })
  @Index()
  path: string;

  @Column({ type: 'text' })
  relativePath: string;

  @Column({ type: 'varchar', length: 50 })
  @Index()
  extension: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  language?: string;

  @Column({ type: 'integer', default: 0 })
  size: number;

  @Column({ type: 'integer', default: 0 })
  lines: number;

  @Column({ type: 'text', nullable: true })
  content?: string;

  @Column({ type: 'varchar', length: 64, nullable: true })
  hash?: string;

  @Column({ type: 'datetime', nullable: true })
  lastModified?: Date;

  @Column({ type: 'boolean', default: false })
  isIndexed: boolean;

  @Column({ type: 'json', nullable: true })
  metadata?: {
    encoding?: string;
    isBinary?: boolean;
    hasErrors?: boolean;
    errorMessage?: string;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Project, project => project.files, { onDelete: 'CASCADE' })
  project: Project;

  @Column()
  projectId: string;

  @OneToMany(() => CodeIndex, codeIndex => codeIndex.file)
  codeIndexes: CodeIndex[];
}
