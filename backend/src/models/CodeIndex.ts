import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, Index } from 'typeorm';
import { File } from './File';

@Entity('code_indexes')
export class CodeIndex {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  @Index()
  type: 'function' | 'class' | 'variable' | 'import' | 'export' | 'interface' | 'type' | 'enum' | 'constant';

  @Column({ type: 'varchar', length: 255 })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  signature?: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'integer' })
  startLine: number;

  @Column({ type: 'integer' })
  endLine: number;

  @Column({ type: 'integer', nullable: true })
  startColumn?: number;

  @Column({ type: 'integer', nullable: true })
  endColumn?: number;

  @Column({ type: 'text', nullable: true })
  content?: string;

  @Column({ type: 'json', nullable: true })
  metadata?: {
    visibility?: 'public' | 'private' | 'protected';
    isStatic?: boolean;
    isAsync?: boolean;
    returnType?: string;
    parameters?: Array<{
      name: string;
      type?: string;
      optional?: boolean;
    }>;
    dependencies?: string[];
    complexity?: number;
  };

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index()
  parentName?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  parentType?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => File, file => file.codeIndexes, { onDelete: 'CASCADE' })
  file: File;

  @Column()
  fileId: string;
}
