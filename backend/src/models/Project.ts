import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { File } from './File';
import { Conversation } from './Conversation';

@Entity('projects')
export class Project {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text' })
  path: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 50, default: 'active' })
  status: 'active' | 'archived' | 'deleted';

  @Column({ type: 'json', nullable: true })
  settings?: {
    excludePatterns?: string[];
    includePatterns?: string[];
    maxFileSize?: number;
    autoIndex?: boolean;
  };

  @Column({ type: 'datetime', nullable: true })
  lastIndexed?: Date;

  @Column({ type: 'integer', default: 0 })
  fileCount: number;

  @Column({ type: 'integer', default: 0 })
  totalLines: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => File, file => file.project)
  files: File[];

  @OneToMany(() => Conversation, conversation => conversation.project)
  conversations: Conversation[];
}
