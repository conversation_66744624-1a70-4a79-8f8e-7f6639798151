import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, Index } from 'typeorm';
import { Conversation } from './Conversation';

@Entity('messages')
export class Message {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50 })
  @Index()
  role: 'user' | 'assistant' | 'system';

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'json', nullable: true })
  metadata?: {
    tokens?: number;
    model?: string;
    temperature?: number;
    executionTime?: number;
    files?: string[];
    actions?: Array<{
      type: string;
      target: string;
      result: string;
    }>;
  };

  @Column({ type: 'varchar', length: 50, default: 'sent' })
  status: 'sending' | 'sent' | 'error' | 'processing';

  @Column({ type: 'text', nullable: true })
  error?: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => Conversation, conversation => conversation.messages, { onDelete: 'CASCADE' })
  conversation: Conversation;

  @Column()
  conversationId: string;
}
