import { DataSource } from 'typeorm';
import path from 'path';
import { Project } from '../models/Project';
import { File } from '../models/File';
import { Conversation } from '../models/Conversation';
import { Message } from '../models/Message';
import { CodeIndex } from '../models/CodeIndex';

export const AppDataSource = new DataSource({
  type: 'sqlite',
  database: process.env.DB_PATH || path.join(__dirname, '../../data/vscodex.db'),
  synchronize: true, // في الإنتاج يجب تعطيل هذا واستخدام migrations
  logging: process.env.NODE_ENV === 'development',
  entities: [Project, File, Conversation, Message, CodeIndex],
  migrations: [path.join(__dirname, '../migrations/*.ts')],
  subscribers: [path.join(__dirname, '../subscribers/*.ts')],
});
