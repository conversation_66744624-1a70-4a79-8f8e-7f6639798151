import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { Project } from '../models/Project';
import { File } from '../models/File';
import { createError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { FileService } from '../services/FileService';
import { IndexingService } from '../services/IndexingService';

export class ProjectController {
  private projectRepo = AppDataSource.getRepository(Project);
  private fileRepo = AppDataSource.getRepository(File);
  private fileService = new FileService();
  private indexingService = new IndexingService();

  getAllProjects = async (req: Request, res: Response) => {
    try {
      const projects = await this.projectRepo.find({
        where: { status: 'active' },
        order: { updatedAt: 'DESC' }
      });

      res.json(projects);
    } catch (error) {
      logger.error('Error fetching projects:', error);
      throw createError('Failed to fetch projects', 500);
    }
  };

  getProjectById = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      
      const project = await this.projectRepo.findOne({
        where: { id },
        relations: ['files', 'conversations']
      });

      if (!project) {
        throw createError('Project not found', 404);
      }

      res.json(project);
    } catch (error) {
      logger.error('Error fetching project:', error);
      throw error;
    }
  };

  createProject = async (req: Request, res: Response) => {
    try {
      const { name, path, description, settings } = req.body;

      if (!name || !path) {
        throw createError('Name and path are required', 400);
      }

      // Check if project with same path already exists
      const existingProject = await this.projectRepo.findOne({
        where: { path }
      });

      if (existingProject) {
        throw createError('Project with this path already exists', 409);
      }

      // Validate path exists
      try {
        const fs = require('fs/promises');
        await fs.access(path);
      } catch (error) {
        throw createError('Project path does not exist or is not accessible', 400);
      }

      const project = this.projectRepo.create({
        name,
        path,
        description,
        settings: {
          excludePatterns: ['node_modules', '.git', 'dist', 'build'],
          includePatterns: ['**/*'],
          maxFileSize: 10485760, // 10MB
          autoIndex: true,
          ...settings
        }
      });

      const savedProject = await this.projectRepo.save(project);

      // Start indexing in background
      if (savedProject.settings?.autoIndex) {
        this.indexingService.indexProject(savedProject.id).catch(error => {
          logger.error(`Background indexing failed for project ${savedProject.id}:`, error);
        });
      }

      res.status(201).json(savedProject);
    } catch (error) {
      logger.error('Error creating project:', error);
      throw error;
    }
  };

  updateProject = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { name, description, settings, status } = req.body;

      const project = await this.projectRepo.findOne({
        where: { id }
      });

      if (!project) {
        throw createError('Project not found', 404);
      }

      // Update fields
      if (name !== undefined) project.name = name;
      if (description !== undefined) project.description = description;
      if (settings !== undefined) project.settings = { ...project.settings, ...settings };
      if (status !== undefined) project.status = status;

      const updatedProject = await this.projectRepo.save(project);

      res.json(updatedProject);
    } catch (error) {
      logger.error('Error updating project:', error);
      throw error;
    }
  };

  deleteProject = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const project = await this.projectRepo.findOne({
        where: { id }
      });

      if (!project) {
        throw createError('Project not found', 404);
      }

      // Soft delete by updating status
      project.status = 'deleted';
      await this.projectRepo.save(project);

      res.json({ message: 'Project deleted successfully' });
    } catch (error) {
      logger.error('Error deleting project:', error);
      throw error;
    }
  };

  indexProject = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const project = await this.projectRepo.findOne({
        where: { id }
      });

      if (!project) {
        throw createError('Project not found', 404);
      }

      // Start indexing
      const jobId = await this.indexingService.indexProject(id);

      res.json({
        message: 'Indexing started',
        jobId,
        projectId: id
      });
    } catch (error) {
      logger.error('Error starting project indexing:', error);
      throw error;
    }
  };

  getProjectStats = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const project = await this.projectRepo.findOne({
        where: { id },
        relations: ['files']
      });

      if (!project) {
        throw createError('Project not found', 404);
      }

      // Calculate statistics
      const files = await this.fileRepo.find({
        where: { projectId: id }
      });

      const stats = {
        totalFiles: files.length,
        totalLines: files.reduce((sum, file) => sum + (file.lines || 0), 0),
        totalSize: files.reduce((sum, file) => sum + (file.size || 0), 0),
        languageBreakdown: this.calculateLanguageBreakdown(files),
        fileTypeBreakdown: this.calculateFileTypeBreakdown(files),
        lastModified: project.updatedAt,
        lastIndexed: project.lastIndexed,
        indexingProgress: await this.indexingService.getIndexingProgress(id)
      };

      res.json(stats);
    } catch (error) {
      logger.error('Error fetching project stats:', error);
      throw error;
    }
  };

  private calculateLanguageBreakdown(files: File[]): Record<string, number> {
    const breakdown: Record<string, number> = {};
    
    for (const file of files) {
      const language = file.language || 'unknown';
      breakdown[language] = (breakdown[language] || 0) + 1;
    }

    return breakdown;
  }

  private calculateFileTypeBreakdown(files: File[]): Record<string, number> {
    const breakdown: Record<string, number> = {};
    
    for (const file of files) {
      const extension = file.extension || 'no-extension';
      breakdown[extension] = (breakdown[extension] || 0) + 1;
    }

    return breakdown;
  }
}
