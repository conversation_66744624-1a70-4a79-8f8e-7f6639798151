import axios from 'axios';
import { AppDataSource } from '../config/database';
import { Conversation } from '../models/Conversation';
import { Message } from '../models/Message';
import { Project } from '../models/Project';
import { File } from '../models/File';
import { CodeIndex } from '../models/CodeIndex';
import { logger } from '../utils/logger';

interface OllamaResponse {
  model: string;
  created_at: string;
  response: string;
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

interface AIContext {
  projectId: string;
  files?: string[];
  functions?: string[];
  classes?: string[];
  variables?: string[];
  recentCode?: string;
}

export class AIService {
  private ollamaHost: string;
  private model: string;
  private conversationRepo = AppDataSource.getRepository(Conversation);
  private messageRepo = AppDataSource.getRepository(Message);
  private projectRepo = AppDataSource.getRepository(Project);
  private fileRepo = AppDataSource.getRepository(File);
  private codeIndexRepo = AppDataSource.getRepository(CodeIndex);

  constructor() {
    this.ollamaHost = process.env.OLLAMA_HOST || 'http://localhost:11434';
    this.model = process.env.OLLAMA_MODEL || 'codellama:7b';
  }

  async processMessage(
    userMessage: string,
    conversationId: string,
    context?: AIContext
  ): Promise<Message> {
    const startTime = Date.now();

    try {
      // Get or create conversation
      let conversation = await this.conversationRepo.findOne({
        where: { id: conversationId },
        relations: ['project', 'messages']
      });

      if (!conversation && context?.projectId) {
        const project = await this.projectRepo.findOne({
          where: { id: context.projectId }
        });

        if (project) {
          conversation = this.conversationRepo.create({
            id: conversationId,
            title: this.generateConversationTitle(userMessage),
            project,
            projectId: context.projectId
          });
          await this.conversationRepo.save(conversation);
        }
      }

      if (!conversation) {
        throw new Error('Conversation not found and could not be created');
      }

      // Save user message
      const userMessageEntity = this.messageRepo.create({
        role: 'user',
        content: userMessage,
        conversation,
        conversationId: conversation.id
      });
      await this.messageRepo.save(userMessageEntity);

      // Build context for AI
      const aiContext = await this.buildAIContext(conversation, context);

      // Generate AI response
      const aiResponse = await this.generateAIResponse(userMessage, aiContext);

      // Save AI message
      const aiMessageEntity = this.messageRepo.create({
        role: 'assistant',
        content: aiResponse.response,
        conversation,
        conversationId: conversation.id,
        metadata: {
          model: this.model,
          executionTime: Date.now() - startTime,
          tokens: aiResponse.eval_count || 0,
          context: context
        }
      });
      await this.messageRepo.save(aiMessageEntity);

      // Update conversation message count
      conversation.messageCount = (conversation.messageCount || 0) + 2;
      await this.conversationRepo.save(conversation);

      logger.info(`AI response generated in ${Date.now() - startTime}ms`);
      return aiMessageEntity;

    } catch (error) {
      logger.error('Error processing AI message:', error);
      
      // Save error message
      if (conversationId) {
        const errorMessage = this.messageRepo.create({
          role: 'assistant',
          content: 'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
          conversationId,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        await this.messageRepo.save(errorMessage);
        return errorMessage;
      }

      throw error;
    }
  }

  private async buildAIContext(
    conversation: Conversation,
    context?: AIContext
  ): Promise<string> {
    let contextParts: string[] = [];

    // Add system prompt
    contextParts.push(`أنت مساعد ذكي للبرمجة تعمل محلياً. تساعد المطورين في:
- كتابة وتحسين الكود
- إصلاح الأخطاء
- شرح المفاهيم البرمجية
- مراجعة الكود
- اقتراح أفضل الممارسات

يرجى الإجابة باللغة العربية عندما يكون ذلك مناسباً، واستخدام أمثلة عملية.`);

    // Add project context
    if (conversation.project) {
      contextParts.push(`\nمعلومات المشروع:
- الاسم: ${conversation.project.name}
- المسار: ${conversation.project.path}
- عدد الملفات: ${conversation.project.fileCount}
- إجمالي الأسطر: ${conversation.project.totalLines}`);
    }

    // Add recent conversation history
    if (conversation.messages && conversation.messages.length > 0) {
      const recentMessages = conversation.messages
        .slice(-6) // Last 6 messages
        .map(msg => `${msg.role === 'user' ? 'المستخدم' : 'المساعد'}: ${msg.content}`)
        .join('\n');
      
      contextParts.push(`\nسجل المحادثة الأخير:\n${recentMessages}`);
    }

    // Add code context if available
    if (context?.files && context.files.length > 0) {
      const fileContents = await this.getFileContents(context.files, conversation.projectId);
      if (fileContents.length > 0) {
        contextParts.push(`\nملفات ذات صلة:\n${fileContents.join('\n\n')}`);
      }
    }

    // Add code index context
    if (context?.functions || context?.classes) {
      const codeContext = await this.getCodeIndexContext(
        conversation.projectId,
        context.functions,
        context.classes
      );
      if (codeContext) {
        contextParts.push(`\nرموز ذات صلة:\n${codeContext}`);
      }
    }

    return contextParts.join('\n\n');
  }

  private async generateAIResponse(
    userMessage: string,
    context: string
  ): Promise<OllamaResponse> {
    const prompt = `${context}\n\nالسؤال: ${userMessage}\n\nالإجابة:`;

    const response = await axios.post(`${this.ollamaHost}/api/generate`, {
      model: this.model,
      prompt,
      stream: false,
      options: {
        temperature: 0.7,
        top_p: 0.9,
        top_k: 40,
        num_predict: 2048,
      }
    }, {
      timeout: 120000 // 2 minutes timeout
    });

    return response.data;
  }

  private async getFileContents(
    filePaths: string[],
    projectId: string
  ): Promise<string[]> {
    const contents: string[] = [];

    for (const filePath of filePaths.slice(0, 5)) { // Limit to 5 files
      try {
        const file = await this.fileRepo.findOne({
          where: { path: filePath, projectId }
        });

        if (file && file.content) {
          const truncatedContent = file.content.length > 2000 
            ? file.content.substring(0, 2000) + '\n... (محتوى مقطوع)'
            : file.content;

          contents.push(`ملف: ${file.name}\n\`\`\`${file.language || ''}\n${truncatedContent}\n\`\`\``);
        }
      } catch (error) {
        logger.error(`Error reading file ${filePath}:`, error);
      }
    }

    return contents;
  }

  private async getCodeIndexContext(
    projectId: string,
    functions?: string[],
    classes?: string[]
  ): Promise<string> {
    const contextParts: string[] = [];

    if (functions && functions.length > 0) {
      const functionIndexes = await this.codeIndexRepo.find({
        where: {
          file: { projectId },
          type: 'function',
          name: functions[0] // For simplicity, just get the first one
        },
        relations: ['file'],
        take: 3
      });

      for (const func of functionIndexes) {
        contextParts.push(`دالة: ${func.name}\nالتوقيع: ${func.signature || 'غير متوفر'}\nالوصف: ${func.description || 'غير متوفر'}`);
      }
    }

    if (classes && classes.length > 0) {
      const classIndexes = await this.codeIndexRepo.find({
        where: {
          file: { projectId },
          type: 'class',
          name: classes[0] // For simplicity, just get the first one
        },
        relations: ['file'],
        take: 3
      });

      for (const cls of classIndexes) {
        contextParts.push(`فئة: ${cls.name}\nالوصف: ${cls.description || 'غير متوفر'}`);
      }
    }

    return contextParts.join('\n\n');
  }

  private generateConversationTitle(firstMessage: string): string {
    // Generate a title based on the first message
    const words = firstMessage.split(' ').slice(0, 5);
    return words.join(' ') + (firstMessage.split(' ').length > 5 ? '...' : '');
  }

  async checkOllamaConnection(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.ollamaHost}/api/tags`, {
        timeout: 5000
      });
      return response.status === 200;
    } catch (error) {
      logger.error('Ollama connection check failed:', error);
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await axios.get(`${this.ollamaHost}/api/tags`);
      return response.data.models?.map((model: any) => model.name) || [];
    } catch (error) {
      logger.error('Error fetching available models:', error);
      return [];
    }
  }

  async pullModel(modelName: string): Promise<void> {
    try {
      await axios.post(`${this.ollamaHost}/api/pull`, {
        name: modelName
      }, {
        timeout: 600000 // 10 minutes for model download
      });
      logger.info(`Model ${modelName} pulled successfully`);
    } catch (error) {
      logger.error(`Error pulling model ${modelName}:`, error);
      throw error;
    }
  }
}
