import { AppDataSource } from '../config/database';
import { Project } from '../models/Project';
import { File } from '../models/File';
import { CodeIndex } from '../models/CodeIndex';
import { FileService } from './FileService';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

interface IndexingJob {
  id: string;
  projectId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: {
    total: number;
    completed: number;
    current?: string;
  };
  startTime: Date;
  endTime?: Date;
  error?: string;
}

export class IndexingService {
  private projectRepo = AppDataSource.getRepository(Project);
  private fileRepo = AppDataSource.getRepository(File);
  private codeIndexRepo = AppDataSource.getRepository(CodeIndex);
  private fileService = new FileService();
  
  private activeJobs = new Map<string, IndexingJob>();

  async indexProject(projectId: string): Promise<string> {
    const project = await this.projectRepo.findOne({
      where: { id: projectId }
    });

    if (!project) {
      throw new Error('Project not found');
    }

    // Check if indexing is already running
    const existingJob = Array.from(this.activeJobs.values())
      .find(job => job.projectId === projectId && job.status === 'running');

    if (existingJob) {
      return existingJob.id;
    }

    // Create new indexing job
    const jobId = uuidv4();
    const job: IndexingJob = {
      id: jobId,
      projectId,
      status: 'pending',
      progress: {
        total: 0,
        completed: 0
      },
      startTime: new Date()
    };

    this.activeJobs.set(jobId, job);

    // Start indexing in background
    this.runIndexing(job).catch(error => {
      logger.error(`Indexing job ${jobId} failed:`, error);
      job.status = 'failed';
      job.error = error.message;
      job.endTime = new Date();
    });

    return jobId;
  }

  private async runIndexing(job: IndexingJob): Promise<void> {
    try {
      job.status = 'running';
      logger.info(`Starting indexing for project ${job.projectId}`);

      const project = await this.projectRepo.findOne({
        where: { id: job.projectId }
      });

      if (!project) {
        throw new Error('Project not found');
      }

      // Get file tree
      const fileTree = await this.fileService.getFileTree(job.projectId);
      const allFiles = this.flattenFileTree(fileTree);
      
      job.progress.total = allFiles.length;

      // Clear existing indexes for this project
      await this.codeIndexRepo.delete({
        file: { projectId: job.projectId }
      });

      // Index each file
      for (const fileNode of allFiles) {
        if (fileNode.type === 'file') {
          try {
            job.progress.current = fileNode.name;
            await this.indexFile(fileNode, job.projectId);
            job.progress.completed++;
          } catch (error) {
            logger.error(`Error indexing file ${fileNode.path}:`, error);
            // Continue with other files
          }
        }
      }

      // Update project
      project.lastIndexed = new Date();
      project.fileCount = allFiles.filter(f => f.type === 'file').length;
      project.totalLines = await this.calculateTotalLines(job.projectId);
      await this.projectRepo.save(project);

      job.status = 'completed';
      job.endTime = new Date();

      logger.info(`Indexing completed for project ${job.projectId}. Processed ${job.progress.completed} files.`);

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.endTime = new Date();
      throw error;
    }
  }

  private flattenFileTree(nodes: any[]): any[] {
    const result: any[] = [];
    
    for (const node of nodes) {
      result.push(node);
      if (node.children) {
        result.push(...this.flattenFileTree(node.children));
      }
    }
    
    return result;
  }

  private async indexFile(fileNode: any, projectId: string): Promise<void> {
    try {
      // Read file content
      const content = await this.fileService.readFile(fileNode.relativePath, projectId);
      
      // Get or create file record
      let file = await this.fileRepo.findOne({
        where: { path: fileNode.path, projectId }
      });

      if (!file) {
        file = this.fileRepo.create({
          name: fileNode.name,
          path: fileNode.path,
          relativePath: fileNode.relativePath,
          extension: fileNode.extension,
          language: fileNode.language,
          size: fileNode.size || 0,
          lines: content.split('\n').length,
          content,
          projectId,
          project: { id: projectId } as any
        });
        file = await this.fileRepo.save(file);
      } else {
        // Update existing file
        file.content = content;
        file.lines = content.split('\n').length;
        file.size = fileNode.size || 0;
        file.isIndexed = false;
        file = await this.fileRepo.save(file);
      }

      // Parse and index code elements
      await this.parseAndIndexCode(file, content);

      // Mark as indexed
      file.isIndexed = true;
      await this.fileRepo.save(file);

    } catch (error) {
      logger.error(`Error indexing file ${fileNode.path}:`, error);
      throw error;
    }
  }

  private async parseAndIndexCode(file: File, content: string): Promise<void> {
    const language = file.language;
    
    if (!language) return;

    try {
      switch (language) {
        case 'javascript':
        case 'typescript':
          await this.parseJavaScriptTypeScript(file, content);
          break;
        case 'python':
          await this.parsePython(file, content);
          break;
        case 'java':
          await this.parseJava(file, content);
          break;
        default:
          // For unsupported languages, do basic parsing
          await this.parseBasic(file, content);
      }
    } catch (error) {
      logger.error(`Error parsing ${language} file ${file.path}:`, error);
      // Fall back to basic parsing
      await this.parseBasic(file, content);
    }
  }

  private async parseJavaScriptTypeScript(file: File, content: string): Promise<void> {
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Function declarations
      const functionMatch = line.match(/^(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\(/);
      if (functionMatch) {
        await this.createCodeIndex(file, 'function', functionMatch[1], i + 1, line);
        continue;
      }

      // Arrow functions
      const arrowMatch = line.match(/^(?:export\s+)?(?:const|let|var)\s+(\w+)\s*=\s*(?:async\s+)?\(/);
      if (arrowMatch) {
        await this.createCodeIndex(file, 'function', arrowMatch[1], i + 1, line);
        continue;
      }

      // Class declarations
      const classMatch = line.match(/^(?:export\s+)?class\s+(\w+)/);
      if (classMatch) {
        await this.createCodeIndex(file, 'class', classMatch[1], i + 1, line);
        continue;
      }

      // Interface declarations (TypeScript)
      const interfaceMatch = line.match(/^(?:export\s+)?interface\s+(\w+)/);
      if (interfaceMatch) {
        await this.createCodeIndex(file, 'interface', interfaceMatch[1], i + 1, line);
        continue;
      }

      // Type declarations (TypeScript)
      const typeMatch = line.match(/^(?:export\s+)?type\s+(\w+)/);
      if (typeMatch) {
        await this.createCodeIndex(file, 'type', typeMatch[1], i + 1, line);
        continue;
      }

      // Import statements
      const importMatch = line.match(/^import\s+.*\s+from\s+['"]([^'"]+)['"]/);
      if (importMatch) {
        await this.createCodeIndex(file, 'import', importMatch[1], i + 1, line);
        continue;
      }
    }
  }

  private async parsePython(file: File, content: string): Promise<void> {
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Function definitions
      const functionMatch = line.match(/^def\s+(\w+)\s*\(/);
      if (functionMatch) {
        await this.createCodeIndex(file, 'function', functionMatch[1], i + 1, line);
        continue;
      }

      // Class definitions
      const classMatch = line.match(/^class\s+(\w+)/);
      if (classMatch) {
        await this.createCodeIndex(file, 'class', classMatch[1], i + 1, line);
        continue;
      }

      // Import statements
      const importMatch = line.match(/^(?:from\s+(\S+)\s+)?import\s+(.+)/);
      if (importMatch) {
        const module = importMatch[1] || importMatch[2];
        await this.createCodeIndex(file, 'import', module, i + 1, line);
        continue;
      }
    }
  }

  private async parseJava(file: File, content: string): Promise<void> {
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Method declarations
      const methodMatch = line.match(/(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(/);
      if (methodMatch && !line.includes('class')) {
        await this.createCodeIndex(file, 'function', methodMatch[1], i + 1, line);
        continue;
      }

      // Class declarations
      const classMatch = line.match(/(?:public|private)?\s*class\s+(\w+)/);
      if (classMatch) {
        await this.createCodeIndex(file, 'class', classMatch[1], i + 1, line);
        continue;
      }

      // Interface declarations
      const interfaceMatch = line.match(/(?:public|private)?\s*interface\s+(\w+)/);
      if (interfaceMatch) {
        await this.createCodeIndex(file, 'interface', interfaceMatch[1], i + 1, line);
        continue;
      }
    }
  }

  private async parseBasic(file: File, content: string): Promise<void> {
    // Basic parsing for unsupported languages
    // Just index obvious patterns like function/class keywords
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (line.includes('function') || line.includes('def') || line.includes('func')) {
        const words = line.split(/\s+/);
        const functionIndex = words.findIndex(word => 
          word === 'function' || word === 'def' || word === 'func'
        );
        if (functionIndex >= 0 && words[functionIndex + 1]) {
          const name = words[functionIndex + 1].replace(/[^\w]/g, '');
          if (name) {
            await this.createCodeIndex(file, 'function', name, i + 1, line);
          }
        }
      }
    }
  }

  private async createCodeIndex(
    file: File,
    type: string,
    name: string,
    lineNumber: number,
    content: string
  ): Promise<void> {
    const codeIndex = this.codeIndexRepo.create({
      type: type as any,
      name,
      startLine: lineNumber,
      endLine: lineNumber,
      content,
      file,
      fileId: file.id
    });

    await this.codeIndexRepo.save(codeIndex);
  }

  private async calculateTotalLines(projectId: string): Promise<number> {
    const result = await this.fileRepo
      .createQueryBuilder('file')
      .select('SUM(file.lines)', 'total')
      .where('file.projectId = :projectId', { projectId })
      .getRawOne();

    return parseInt(result.total) || 0;
  }

  async getIndexingProgress(projectId: string): Promise<IndexingJob['progress'] | null> {
    const job = Array.from(this.activeJobs.values())
      .find(job => job.projectId === projectId);

    return job ? job.progress : null;
  }

  async getIndexingStatus(jobId: string): Promise<IndexingJob | null> {
    return this.activeJobs.get(jobId) || null;
  }
}
