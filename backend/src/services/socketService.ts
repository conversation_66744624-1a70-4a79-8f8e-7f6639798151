import { Server, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { AIService } from './AIService';
import { FileService } from './FileService';

interface SocketData {
  userId?: string;
  projectId?: string;
}

export const setupSocketHandlers = (io: Server) => {
  const aiService = new AIService();
  const fileService = new FileService();

  io.on('connection', (socket: Socket<any, any, any, SocketData>) => {
    logger.info(`Client connected: ${socket.id}`);

    // Join project room
    socket.on('join-project', (projectId: string) => {
      socket.data.projectId = projectId;
      socket.join(`project:${projectId}`);
      logger.info(`Socket ${socket.id} joined project ${projectId}`);
    });

    // Leave project room
    socket.on('leave-project', (projectId: string) => {
      socket.leave(`project:${projectId}`);
      logger.info(`Socket ${socket.id} left project ${projectId}`);
    });

    // Handle AI chat messages
    socket.on('ai-message', async (data: {
      message: string;
      conversationId: string;
      context?: any;
    }) => {
      try {
        logger.info(`AI message from ${socket.id}: ${data.message.substring(0, 100)}...`);
        
        // Emit typing indicator
        socket.emit('ai-typing', true);

        // Process message with AI
        const response = await aiService.processMessage(
          data.message,
          data.conversationId,
          data.context
        );

        // Stop typing indicator
        socket.emit('ai-typing', false);

        // Send response
        socket.emit('ai-response', {
          message: response.content,
          messageId: response.id,
          metadata: response.metadata
        });

        // Broadcast to project room if needed
        if (socket.data.projectId) {
          socket.to(`project:${socket.data.projectId}`).emit('project-activity', {
            type: 'ai-interaction',
            userId: socket.data.userId,
            timestamp: new Date()
          });
        }

      } catch (error) {
        logger.error('Error processing AI message:', error);
        socket.emit('ai-error', {
          message: 'حدث خطأ أثناء معالجة الرسالة',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        socket.emit('ai-typing', false);
      }
    });

    // Handle file operations
    socket.on('file-operation', async (data: {
      operation: 'read' | 'write' | 'create' | 'delete';
      path: string;
      content?: string;
      projectId: string;
    }) => {
      try {
        logger.info(`File operation from ${socket.id}: ${data.operation} on ${data.path}`);

        let result;
        switch (data.operation) {
          case 'read':
            result = await fileService.readFile(data.path, data.projectId);
            break;
          case 'write':
            result = await fileService.writeFile(data.path, data.content || '', data.projectId);
            break;
          case 'create':
            result = await fileService.createFile(data.path, data.content || '', data.projectId);
            break;
          case 'delete':
            result = await fileService.deleteFile(data.path, data.projectId);
            break;
        }

        socket.emit('file-operation-result', {
          operation: data.operation,
          path: data.path,
          success: true,
          result
        });

        // Broadcast file change to project room
        if (socket.data.projectId) {
          socket.to(`project:${socket.data.projectId}`).emit('file-changed', {
            operation: data.operation,
            path: data.path,
            userId: socket.data.userId,
            timestamp: new Date()
          });
        }

      } catch (error) {
        logger.error('Error in file operation:', error);
        socket.emit('file-operation-result', {
          operation: data.operation,
          path: data.path,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Handle project indexing progress
    socket.on('start-indexing', async (projectId: string) => {
      try {
        // This would be implemented in the indexing service
        socket.emit('indexing-progress', { progress: 0, status: 'بدء الفهرسة...' });
        
        // Simulate indexing progress
        for (let i = 0; i <= 100; i += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          socket.emit('indexing-progress', { 
            progress: i, 
            status: `فهرسة الملفات... ${i}%` 
          });
        }

        socket.emit('indexing-complete', { 
          success: true, 
          message: 'تمت الفهرسة بنجاح' 
        });

      } catch (error) {
        logger.error('Error in indexing:', error);
        socket.emit('indexing-complete', { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      logger.info(`Client disconnected: ${socket.id}`);
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error(`Socket error for ${socket.id}:`, error);
    });
  });
};
