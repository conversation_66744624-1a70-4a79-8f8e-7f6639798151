import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import { AppDataSource } from '../config/database';
import { File } from '../models/File';
import { Project } from '../models/Project';
import { logger } from '../utils/logger';

interface FileNode {
  id: string;
  name: string;
  path: string;
  relativePath: string;
  type: 'file' | 'directory';
  extension?: string;
  language?: string;
  size?: number;
  lines?: number;
  lastModified?: Date;
  children?: FileNode[];
}

export class FileService {
  private fileRepo = AppDataSource.getRepository(File);
  private projectRepo = AppDataSource.getRepository(Project);

  private readonly SUPPORTED_EXTENSIONS = new Set([
    '.js', '.jsx', '.ts', '.tsx',
    '.py', '.java', '.cpp', '.c', '.h', '.hpp',
    '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt',
    '.html', '.css', '.scss', '.sass', '.less',
    '.json', '.xml', '.yaml', '.yml', '.md', '.txt',
    '.sql', '.sh', '.bash', '.ps1', '.dockerfile'
  ]);

  private readonly LANGUAGE_MAP: Record<string, string> = {
    '.js': 'javascript',
    '.jsx': 'javascript',
    '.ts': 'typescript',
    '.tsx': 'typescript',
    '.py': 'python',
    '.java': 'java',
    '.cpp': 'cpp',
    '.c': 'c',
    '.h': 'c',
    '.hpp': 'cpp',
    '.cs': 'csharp',
    '.php': 'php',
    '.rb': 'ruby',
    '.go': 'go',
    '.rs': 'rust',
    '.swift': 'swift',
    '.kt': 'kotlin',
    '.html': 'html',
    '.css': 'css',
    '.scss': 'scss',
    '.sass': 'sass',
    '.less': 'less',
    '.json': 'json',
    '.xml': 'xml',
    '.yaml': 'yaml',
    '.yml': 'yaml',
    '.md': 'markdown',
    '.sql': 'sql',
    '.sh': 'shell',
    '.bash': 'shell',
    '.ps1': 'powershell'
  };

  async getFileTree(projectId: string): Promise<FileNode[]> {
    const project = await this.projectRepo.findOne({
      where: { id: projectId }
    });

    if (!project) {
      throw new Error('Project not found');
    }

    try {
      const tree = await this.buildFileTree(project.path, project.path);
      return tree;
    } catch (error) {
      logger.error(`Error building file tree for project ${projectId}:`, error);
      throw error;
    }
  }

  private async buildFileTree(
    currentPath: string,
    rootPath: string,
    depth: number = 0
  ): Promise<FileNode[]> {
    if (depth > 10) return []; // Prevent infinite recursion

    const nodes: FileNode[] = [];

    try {
      const entries = await fs.readdir(currentPath, { withFileTypes: true });

      for (const entry of entries) {
        // Skip hidden files and common ignore patterns
        if (this.shouldIgnore(entry.name)) continue;

        const fullPath = path.join(currentPath, entry.name);
        const relativePath = path.relative(rootPath, fullPath);
        const extension = path.extname(entry.name).toLowerCase();

        if (entry.isDirectory()) {
          const children = await this.buildFileTree(fullPath, rootPath, depth + 1);
          
          nodes.push({
            id: crypto.createHash('md5').update(fullPath).digest('hex'),
            name: entry.name,
            path: fullPath,
            relativePath,
            type: 'directory',
            children
          });
        } else if (entry.isFile() && this.SUPPORTED_EXTENSIONS.has(extension)) {
          const stats = await fs.stat(fullPath);
          
          nodes.push({
            id: crypto.createHash('md5').update(fullPath).digest('hex'),
            name: entry.name,
            path: fullPath,
            relativePath,
            type: 'file',
            extension,
            language: this.LANGUAGE_MAP[extension],
            size: stats.size,
            lastModified: stats.mtime
          });
        }
      }

      // Sort: directories first, then files, both alphabetically
      return nodes.sort((a, b) => {
        if (a.type !== b.type) {
          return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
      });

    } catch (error) {
      logger.error(`Error reading directory ${currentPath}:`, error);
      return [];
    }
  }

  private shouldIgnore(name: string): boolean {
    const ignorePatterns = [
      /^\./,                    // Hidden files
      /^node_modules$/,         // Node.js
      /^\.git$/,               // Git
      /^\.vscode$/,            // VS Code
      /^\.idea$/,              // IntelliJ
      /^__pycache__$/,         // Python
      /^\.pytest_cache$/,      // Python
      /^venv$/,                // Python virtual env
      /^env$/,                 // Environment
      /^dist$/,                // Build output
      /^build$/,               // Build output
      /^target$/,              // Rust/Java
      /^bin$/,                 // Binaries
      /^obj$/,                 // Object files
      /^\.next$/,              // Next.js
      /^\.nuxt$/,              // Nuxt.js
      /^coverage$/,            // Test coverage
      /^logs$/,                // Log files
      /\.log$/,                // Log files
      /\.tmp$/,                // Temporary files
      /\.temp$/,               // Temporary files
      /\.cache$/,              // Cache files
      /\.DS_Store$/,           // macOS
      /^Thumbs\.db$/,          // Windows
    ];

    return ignorePatterns.some(pattern => pattern.test(name));
  }

  async readFile(filePath: string, projectId: string): Promise<string> {
    const project = await this.projectRepo.findOne({
      where: { id: projectId }
    });

    if (!project) {
      throw new Error('Project not found');
    }

    // Ensure the file is within the project directory
    const fullPath = path.resolve(project.path, filePath);
    if (!fullPath.startsWith(path.resolve(project.path))) {
      throw new Error('File path is outside project directory');
    }

    try {
      const content = await fs.readFile(fullPath, 'utf-8');
      
      // Update or create file record
      await this.updateFileRecord(fullPath, project, content);
      
      return content;
    } catch (error) {
      logger.error(`Error reading file ${fullPath}:`, error);
      throw error;
    }
  }

  async writeFile(filePath: string, content: string, projectId: string): Promise<void> {
    const project = await this.projectRepo.findOne({
      where: { id: projectId }
    });

    if (!project) {
      throw new Error('Project not found');
    }

    // Ensure the file is within the project directory
    const fullPath = path.resolve(project.path, filePath);
    if (!fullPath.startsWith(path.resolve(project.path))) {
      throw new Error('File path is outside project directory');
    }

    try {
      // Ensure directory exists
      await fs.mkdir(path.dirname(fullPath), { recursive: true });
      
      // Write file
      await fs.writeFile(fullPath, content, 'utf-8');
      
      // Update file record
      await this.updateFileRecord(fullPath, project, content);
      
      logger.info(`File written: ${fullPath}`);
    } catch (error) {
      logger.error(`Error writing file ${fullPath}:`, error);
      throw error;
    }
  }

  async createFile(filePath: string, content: string, projectId: string): Promise<void> {
    const project = await this.projectRepo.findOne({
      where: { id: projectId }
    });

    if (!project) {
      throw new Error('Project not found');
    }

    const fullPath = path.resolve(project.path, filePath);
    if (!fullPath.startsWith(path.resolve(project.path))) {
      throw new Error('File path is outside project directory');
    }

    try {
      // Check if file already exists
      try {
        await fs.access(fullPath);
        throw new Error('File already exists');
      } catch (error: any) {
        if (error.code !== 'ENOENT') {
          throw error;
        }
      }

      // Create file
      await this.writeFile(filePath, content, projectId);
      
      logger.info(`File created: ${fullPath}`);
    } catch (error) {
      logger.error(`Error creating file ${fullPath}:`, error);
      throw error;
    }
  }

  async deleteFile(filePath: string, projectId: string): Promise<void> {
    const project = await this.projectRepo.findOne({
      where: { id: projectId }
    });

    if (!project) {
      throw new Error('Project not found');
    }

    const fullPath = path.resolve(project.path, filePath);
    if (!fullPath.startsWith(path.resolve(project.path))) {
      throw new Error('File path is outside project directory');
    }

    try {
      // Delete file from filesystem
      await fs.unlink(fullPath);
      
      // Delete file record from database
      await this.fileRepo.delete({
        path: fullPath,
        projectId
      });
      
      logger.info(`File deleted: ${fullPath}`);
    } catch (error) {
      logger.error(`Error deleting file ${fullPath}:`, error);
      throw error;
    }
  }

  private async updateFileRecord(
    fullPath: string,
    project: Project,
    content: string
  ): Promise<File> {
    const stats = await fs.stat(fullPath);
    const extension = path.extname(fullPath).toLowerCase();
    const relativePath = path.relative(project.path, fullPath);
    const hash = crypto.createHash('md5').update(content).digest('hex');
    const lines = content.split('\n').length;

    let file = await this.fileRepo.findOne({
      where: { path: fullPath, projectId: project.id }
    });

    if (file) {
      // Update existing file
      file.size = stats.size;
      file.lines = lines;
      file.content = content;
      file.hash = hash;
      file.lastModified = stats.mtime;
      file.isIndexed = false; // Mark for re-indexing
    } else {
      // Create new file record
      file = this.fileRepo.create({
        name: path.basename(fullPath),
        path: fullPath,
        relativePath,
        extension,
        language: this.LANGUAGE_MAP[extension],
        size: stats.size,
        lines,
        content,
        hash,
        lastModified: stats.mtime,
        project,
        projectId: project.id,
        isIndexed: false
      });
    }

    return await this.fileRepo.save(file);
  }

  async getFileStats(filePath: string, projectId: string): Promise<{
    size: number;
    lines: number;
    characters: number;
    words: number;
    language: string;
  }> {
    const content = await this.readFile(filePath, projectId);
    const extension = path.extname(filePath).toLowerCase();
    
    return {
      size: Buffer.byteLength(content, 'utf-8'),
      lines: content.split('\n').length,
      characters: content.length,
      words: content.split(/\s+/).filter(word => word.length > 0).length,
      language: this.LANGUAGE_MAP[extension] || 'plaintext'
    };
  }

  async searchFiles(projectId: string, query: string): Promise<FileNode[]> {
    const tree = await this.getFileTree(projectId);
    return this.filterFileTree(tree, query.toLowerCase());
  }

  private filterFileTree(nodes: FileNode[], query: string): FileNode[] {
    const results: FileNode[] = [];

    for (const node of nodes) {
      if (node.name.toLowerCase().includes(query)) {
        results.push(node);
      } else if (node.children) {
        const filteredChildren = this.filterFileTree(node.children, query);
        if (filteredChildren.length > 0) {
          results.push({
            ...node,
            children: filteredChildren
          });
        }
      }
    }

    return results;
  }
}
