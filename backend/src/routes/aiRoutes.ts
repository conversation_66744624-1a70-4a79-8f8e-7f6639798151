import { Router } from 'express';
import { AIController } from '../controllers/AIController';
import { asyncHand<PERSON> } from '../middleware/errorHandler';

const router = Router();
const aiController = new AIController();

// POST /api/v1/ai/chat - Send message to AI
router.post('/chat', asyncHand<PERSON>(aiController.chat));

// GET /api/v1/ai/status - Get AI service status
router.get('/status', asyncHandler(aiController.getStatus));

// GET /api/v1/ai/models - Get available AI models
router.get('/models', asyncHandler(aiController.getAvailableModels));

// POST /api/v1/ai/models/pull - Pull/download a model
router.post('/models/pull', asyncHandler(aiController.pullModel));

// POST /api/v1/ai/code/complete - Code completion
router.post('/code/complete', async<PERSON>and<PERSON>(aiController.codeComplete));

// POST /api/v1/ai/code/explain - Explain code
router.post('/code/explain', asyncHandler(aiController.explainCode));

// POST /api/v1/ai/code/review - Review code
router.post('/code/review', asyncHandler(aiController.reviewCode));

// POST /api/v1/ai/code/fix - Fix code issues
router.post('/code/fix', asyncHandler(aiController.fixCode));

// POST /api/v1/ai/code/generate - Generate code
router.post('/code/generate', asyncHandler(aiController.generateCode));

// POST /api/v1/ai/code/refactor - Refactor code
router.post('/code/refactor', asyncHandler(aiController.refactorCode));

export default router;
