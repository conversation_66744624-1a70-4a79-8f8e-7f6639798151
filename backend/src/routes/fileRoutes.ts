import { Router } from 'express';
import { FileController } from '../controllers/FileController';
import { asyncHand<PERSON> } from '../middleware/errorHandler';

const router = Router();
const fileController = new FileController();

// GET /api/v1/files/tree/:projectId - Get file tree for project
router.get('/tree/:projectId', async<PERSON><PERSON><PERSON>(fileController.getFileTree));

// GET /api/v1/files/:id/content - Get file content by ID
router.get('/:id/content', asyncHandler(fileController.getFileContent));

// GET /api/v1/files/content - Get file content by path
router.get('/content', asyncHandler(fileController.getFileContentByPath));

// POST /api/v1/files - Create new file
router.post('/', asyncHandler(fileController.createFile));

// PUT /api/v1/files/:id - Update file content by ID
router.put('/:id', async<PERSON><PERSON><PERSON>(fileController.updateFile));

// PUT /api/v1/files/content - Update file content by path
router.put('/content', asyncHandler(fileController.updateFileByPath));

// DELETE /api/v1/files/:id - Delete file by ID
router.delete('/:id', asyncHandler(fileController.deleteFile));

// DELETE /api/v1/files/content - Delete file by path
router.delete('/content', asyncHandler(fileController.deleteFileByPath));

// GET /api/v1/files/search - Search files
router.get('/search', asyncHandler(fileController.searchFiles));

// GET /api/v1/files/search-content - Search in file content
router.get('/search-content', asyncHandler(fileController.searchInFiles));

// GET /api/v1/files/:id/stats - Get file statistics
router.get('/:id/stats', asyncHandler(fileController.getFileStats));

// GET /api/v1/files/recent - Get recent files
router.get('/recent', asyncHandler(fileController.getRecentFiles));

// PATCH /api/v1/files/:id/rename - Rename file
router.patch('/:id/rename', asyncHandler(fileController.renameFile));

// PATCH /api/v1/files/:id/move - Move file
router.patch('/:id/move', asyncHandler(fileController.moveFile));

// POST /api/v1/files/:id/copy - Copy file
router.post('/:id/copy', asyncHandler(fileController.copyFile));

// POST /api/v1/files/:id/format - Format file content
router.post('/:id/format', asyncHandler(fileController.formatFile));

// POST /api/v1/files/:id/validate - Validate file syntax
router.post('/:id/validate', asyncHandler(fileController.validateFile));

export default router;
