import { Express } from 'express';
import projectRoutes from './projectRoutes';
import fileRoutes from './fileRoutes';
import conversationRoutes from './conversationRoutes';
import aiRoutes from './aiRoutes';

export const setupRoutes = (app: Express) => {
  // API prefix
  const apiPrefix = '/api/v1';

  // Health check (already defined in main index.ts)
  
  // Project routes
  app.use(`${apiPrefix}/projects`, projectRoutes);
  
  // File routes
  app.use(`${apiPrefix}/files`, fileRoutes);
  
  // Conversation routes
  app.use(`${apiPrefix}/conversations`, conversationRoutes);
  
  // AI routes
  app.use(`${apiPrefix}/ai`, aiRoutes);
};
