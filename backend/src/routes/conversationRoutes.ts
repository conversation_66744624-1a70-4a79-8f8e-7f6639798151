import { Router } from 'express';
import { ConversationController } from '../controllers/ConversationController';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();
const conversationController = new ConversationController();

// GET /api/v1/conversations - Get all conversations
router.get('/', async<PERSON>and<PERSON>(conversationController.getAllConversations));

// POST /api/v1/conversations - Create new conversation
router.post('/', asyncHandler(conversationController.createConversation));

// GET /api/v1/conversations/:id - Get conversation by ID
router.get('/:id', asyncHandler(conversationController.getConversationById));

// PUT /api/v1/conversations/:id - Update conversation
router.put('/:id', asyncHandler(conversationController.updateConversation));

// DELETE /api/v1/conversations/:id - Delete conversation
router.delete('/:id', as<PERSON><PERSON><PERSON><PERSON>(conversationController.deleteConversation));

// GET /api/v1/conversations/:id/messages - Get conversation messages
router.get('/:id/messages', async<PERSON><PERSON><PERSON>(conversationController.getConversationMessages));

// POST /api/v1/conversations/:id/messages - Add message to conversation
router.post('/:id/messages', asyncHandler(conversationController.addMessage));

// GET /api/v1/conversations/project/:projectId - Get conversations for project
router.get('/project/:projectId', asyncHandler(conversationController.getProjectConversations));

export default router;
