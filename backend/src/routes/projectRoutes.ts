import { Router } from 'express';
import { ProjectController } from '../controllers/ProjectController';
import { asyncHand<PERSON> } from '../middleware/errorHandler';

const router = Router();
const projectController = new ProjectController();

// GET /api/v1/projects - Get all projects
router.get('/', async<PERSON>and<PERSON>(projectController.getAllProjects));

// POST /api/v1/projects - Create new project
router.post('/', async<PERSON>andler(projectController.createProject));

// GET /api/v1/projects/:id - Get project by ID
router.get('/:id', asyncHandler(projectController.getProjectById));

// PUT /api/v1/projects/:id - Update project
router.put('/:id', asyncHandler(projectController.updateProject));

// DELETE /api/v1/projects/:id - Delete project
router.delete('/:id', async<PERSON>and<PERSON>(projectController.deleteProject));

// POST /api/v1/projects/:id/index - Index project files
router.post('/:id/index', async<PERSON><PERSON><PERSON>(projectController.indexProject));

// GET /api/v1/projects/:id/stats - Get project statistics
router.get('/:id/stats', asyncHandler(projectController.getProjectStats));

export default router;
