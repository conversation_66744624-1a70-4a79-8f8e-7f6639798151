{"name": "vscodex-backend", "version": "1.0.0", "description": "Backend for VSCodeX - Local Augment Coder System", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.5", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "typeorm": "^0.3.17", "reflect-metadata": "^0.1.13", "chokidar": "^3.5.3", "tree-sitter": "^0.20.4", "tree-sitter-javascript": "^0.20.2", "tree-sitter-typescript": "^0.20.3", "tree-sitter-python": "^0.20.4", "tree-sitter-java": "^0.20.2", "tree-sitter-cpp": "^0.20.0", "tree-sitter-rust": "^0.20.4", "tree-sitter-go": "^0.20.0", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "winston": "^3.11.0", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.5", "@types/multer": "^1.4.11", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1"}}