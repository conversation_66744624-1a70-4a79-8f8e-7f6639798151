#!/bin/bash

# VSCodeX Testing Script
# نص اختبار VSCodeX

set -e

echo "🧪 بدء اختبار VSCodeX..."
echo "🧪 Starting VSCodeX tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_status "تشغيل: $test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_success "$test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "$test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Test 1: Check Node.js installation
print_status "فحص متطلبات النظام..."
print_status "Checking system requirements..."

run_test "Node.js متوفر" "command -v node"
run_test "npm متوفر" "command -v npm"

if command -v node &> /dev/null; then
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -ge 18 ]; then
        print_success "إصدار Node.js مناسب: $(node -v)"
    else
        print_error "إصدار Node.js قديم: $(node -v). يتطلب 18+"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
fi

# Test 2: Check project structure
print_status "فحص هيكل المشروع..."
print_status "Checking project structure..."

run_test "ملف package.json موجود" "test -f package.json"
run_test "مجلد backend موجود" "test -d backend"
run_test "مجلد frontend موجود" "test -d frontend"
run_test "ملف README.md موجود" "test -f README.md"
run_test "ملف docker-compose.yml موجود" "test -f docker-compose.yml"

# Test 3: Check backend structure
print_status "فحص هيكل Backend..."
print_status "Checking backend structure..."

run_test "backend/package.json موجود" "test -f backend/package.json"
run_test "backend/tsconfig.json موجود" "test -f backend/tsconfig.json"
run_test "backend/src موجود" "test -d backend/src"
run_test "backend/src/index.ts موجود" "test -f backend/src/index.ts"

# Test 4: Check frontend structure
print_status "فحص هيكل Frontend..."
print_status "Checking frontend structure..."

run_test "frontend/package.json موجود" "test -f frontend/package.json"
run_test "frontend/tsconfig.json موجود" "test -f frontend/tsconfig.json"
run_test "frontend/src موجود" "test -d frontend/src"
run_test "frontend/src/app موجود" "test -d frontend/src/app"

# Test 5: Check dependencies installation
print_status "فحص تثبيت التبعيات..."
print_status "Checking dependencies installation..."

if [ -d "node_modules" ]; then
    print_success "تبعيات الجذر مثبتة"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "تبعيات الجذر غير مثبتة"
fi

if [ -d "backend/node_modules" ]; then
    print_success "تبعيات Backend مثبتة"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "تبعيات Backend غير مثبتة"
fi

if [ -d "frontend/node_modules" ]; then
    print_success "تبعيات Frontend مثبتة"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "تبعيات Frontend غير مثبتة"
fi

TOTAL_TESTS=$((TOTAL_TESTS + 3))

# Test 6: TypeScript compilation
print_status "اختبار تجميع TypeScript..."
print_status "Testing TypeScript compilation..."

if [ -d "backend/node_modules" ]; then
    cd backend
    if npm run build > /dev/null 2>&1; then
        print_success "تجميع Backend نجح"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_error "تجميع Backend فشل"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    cd ..
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

if [ -d "frontend/node_modules" ]; then
    cd frontend
    if npm run build > /dev/null 2>&1; then
        print_success "تجميع Frontend نجح"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_error "تجميع Frontend فشل"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    cd ..
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

# Test 7: Check Ollama
print_status "فحص Ollama..."
print_status "Checking Ollama..."

if command -v ollama &> /dev/null; then
    print_success "Ollama مثبت"
    PASSED_TESTS=$((PASSED_TESTS + 1))
    
    # Check if Ollama is running
    if pgrep -f "ollama serve" > /dev/null; then
        print_success "Ollama يعمل"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_warning "Ollama غير مشغل"
    fi
    
    # Check if model is available
    if ollama list | grep -q "codellama" 2>/dev/null; then
        print_success "نموذج CodeLlama متوفر"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_warning "نموذج CodeLlama غير متوفر"
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 3))
else
    print_warning "Ollama غير مثبت"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

# Test 8: API Health Check (if running)
print_status "فحص صحة API..."
print_status "Checking API health..."

if curl -s http://localhost:3001/health > /dev/null 2>&1; then
    print_success "API يعمل بشكل صحيح"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "API غير متاح (قد يكون غير مشغل)"
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test 9: Frontend Health Check (if running)
if curl -s http://localhost:3000/api/health > /dev/null 2>&1; then
    print_success "Frontend يعمل بشكل صحيح"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "Frontend غير متاح (قد يكون غير مشغل)"
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Test 10: Environment files
print_status "فحص ملفات البيئة..."
print_status "Checking environment files..."

if [ -f "backend/.env" ]; then
    print_success "ملف .env موجود"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    print_warning "ملف .env غير موجود"
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

# Summary
echo ""
echo "📊 ملخص الاختبارات:"
echo "📊 Test Summary:"
echo "=================="
echo "إجمالي الاختبارات: $TOTAL_TESTS"
echo "Total Tests: $TOTAL_TESTS"
echo "نجح: $PASSED_TESTS"
echo "Passed: $PASSED_TESTS"
echo "فشل: $FAILED_TESTS"
echo "Failed: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    print_success "🎉 جميع الاختبارات نجحت!"
    print_success "🎉 All tests passed!"
    exit 0
else
    print_error "❌ بعض الاختبارات فشلت"
    print_error "❌ Some tests failed"
    exit 1
fi
