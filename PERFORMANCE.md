# دليل تحسين الأداء - VSCodeX

هذا الدليل يحتوي على نصائح وإرشادات لتحسين أداء VSCodeX.

## تحسين Backend

### 1. قاعدة البيانات

#### SQLite Optimization
```javascript
// في ملف database.ts
export const AppDataSource = new DataSource({
  type: 'sqlite',
  database: process.env.DB_PATH || './data/vscodex.db',
  synchronize: false, // تعطيل في الإنتاج
  logging: false, // تعطيل في الإنتاج
  cache: {
    duration: 30000 // 30 ثانية
  },
  extra: {
    // تحسينات SQLite
    "journal_mode": "WAL",
    "synchronous": "NORMAL",
    "cache_size": 1000,
    "temp_store": "memory"
  }
});
```

#### فهرسة قاعدة البيانات
```sql
-- إضافة فهارس للاستعلامات السريعة
CREATE INDEX idx_files_project_id ON files(projectId);
CREATE INDEX idx_files_path ON files(path);
CREATE INDEX idx_code_indexes_file_id ON code_indexes(fileId);
CREATE INDEX idx_code_indexes_type ON code_indexes(type);
CREATE INDEX idx_messages_conversation_id ON messages(conversationId);
```

### 2. تحسين الذاكرة

#### تحديد حجم الملفات
```typescript
// في FileService.ts
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_CONTENT_LENGTH = 1000000; // 1M characters

async readFile(filePath: string): Promise<string> {
  const stats = await fs.stat(filePath);
  
  if (stats.size > MAX_FILE_SIZE) {
    throw new Error('File too large');
  }
  
  const content = await fs.readFile(filePath, 'utf-8');
  
  if (content.length > MAX_CONTENT_LENGTH) {
    return content.substring(0, MAX_CONTENT_LENGTH) + '\n... (truncated)';
  }
  
  return content;
}
```

#### تنظيف الذاكرة
```typescript
// تنظيف دوري للذاكرة
setInterval(() => {
  if (global.gc) {
    global.gc();
  }
}, 300000); // كل 5 دقائق
```

### 3. تحسين الشبكة

#### ضغط الاستجابات
```typescript
import compression from 'compression';

app.use(compression({
  level: 6,
  threshold: 1024,
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }
}));
```

#### تحديد معدل الطلبات
```typescript
import rateLimit from 'express-rate-limit';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 1000, // حد أقصى 1000 طلب لكل IP
  message: 'Too many requests from this IP'
});

app.use('/api/', limiter);
```

## تحسين Frontend

### 1. تحسين React

#### استخدام React.memo
```typescript
import React, { memo } from 'react';

export const FileNode = memo(({ file, onSelect }: FileNodeProps) => {
  // Component logic
});
```

#### تحسين re-renders
```typescript
import { useCallback, useMemo } from 'react';

const FileExplorer = ({ files }: Props) => {
  const filteredFiles = useMemo(() => {
    return files.filter(file => file.name.includes(searchQuery));
  }, [files, searchQuery]);

  const handleFileSelect = useCallback((file: File) => {
    onFileSelect(file);
  }, [onFileSelect]);

  return (
    // JSX
  );
};
```

### 2. تحسين Monaco Editor

#### تحميل كسول للغات
```typescript
import { loader } from '@monaco-editor/react';

// تحميل اللغات عند الحاجة فقط
const loadLanguage = async (language: string) => {
  const monaco = await loader.init();
  
  switch (language) {
    case 'python':
      await import('monaco-editor/esm/vs/basic-languages/python/python.contribution');
      break;
    case 'java':
      await import('monaco-editor/esm/vs/basic-languages/java/java.contribution');
      break;
  }
};
```

#### تحسين إعدادات المحرر
```typescript
const editorOptions = {
  minimap: { enabled: false }, // تعطيل الخريطة المصغرة للملفات الصغيرة
  scrollBeyondLastLine: false,
  renderWhitespace: 'selection',
  automaticLayout: true,
  fontSize: 14,
  lineHeight: 20,
  wordWrap: 'on',
  // تحسين الأداء
  renderLineHighlight: 'line',
  renderIndentGuides: true,
  smoothScrolling: true,
  cursorBlinking: 'smooth',
  // تقليل استهلاك الذاكرة
  maxTokenizationLineLength: 20000,
  largeFileOptimizations: true
};
```

### 3. تحسين التحميل

#### Code Splitting
```typescript
import { lazy, Suspense } from 'react';

const CodeEditor = lazy(() => import('./CodeEditor'));
const FileExplorer = lazy(() => import('./FileExplorer'));

const App = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <CodeEditor />
    <FileExplorer />
  </Suspense>
);
```

#### تحسين الصور والأصول
```typescript
// في next.config.js
module.exports = {
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },
  experimental: {
    optimizeCss: true,
  },
};
```

## تحسين Ollama/AI

### 1. إعدادات النموذج

#### تحسين معاملات النموذج
```typescript
const ollamaOptions = {
  temperature: 0.7, // توازن بين الإبداع والدقة
  top_p: 0.9,
  top_k: 40,
  num_predict: 1024, // تقليل عدد الرموز المتوقعة
  repeat_penalty: 1.1,
  num_ctx: 4096, // حجم السياق
};
```

#### تخزين مؤقت للاستجابات
```typescript
import NodeCache from 'node-cache';

const responseCache = new NodeCache({ 
  stdTTL: 600, // 10 دقائق
  maxKeys: 1000 
});

async generateResponse(prompt: string): Promise<string> {
  const cacheKey = crypto.createHash('md5').update(prompt).digest('hex');
  
  const cached = responseCache.get(cacheKey);
  if (cached) {
    return cached as string;
  }
  
  const response = await this.callOllama(prompt);
  responseCache.set(cacheKey, response);
  
  return response;
}
```

### 2. إدارة الذاكرة

#### تنظيف السياق
```typescript
class AIService {
  private contextHistory = new Map<string, string[]>();
  
  private cleanupContext(conversationId: string) {
    const history = this.contextHistory.get(conversationId) || [];
    
    // الاحتفاظ بآخر 10 رسائل فقط
    if (history.length > 10) {
      this.contextHistory.set(conversationId, history.slice(-10));
    }
  }
}
```

## مراقبة الأداء

### 1. مقاييس Backend

```typescript
import { performance } from 'perf_hooks';

const performanceMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = performance.now();
  
  res.on('finish', () => {
    const duration = performance.now() - start;
    logger.info(`${req.method} ${req.path} - ${duration.toFixed(2)}ms`);
  });
  
  next();
};
```

### 2. مقاييس Frontend

```typescript
// قياس أداء المكونات
import { Profiler } from 'react';

const onRenderCallback = (id: string, phase: string, actualDuration: number) => {
  console.log(`Component ${id} took ${actualDuration}ms to ${phase}`);
};

<Profiler id="CodeEditor" onRender={onRenderCallback}>
  <CodeEditor />
</Profiler>
```

### 3. مراقبة الذاكرة

```typescript
// مراقبة استهلاك الذاكرة
setInterval(() => {
  const memUsage = process.memoryUsage();
  logger.info('Memory usage:', {
    rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB',
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB',
    external: Math.round(memUsage.external / 1024 / 1024) + ' MB'
  });
}, 60000); // كل دقيقة
```

## نصائح عامة

### 1. تحسين قاعدة البيانات
- استخدم فهارس مناسبة
- تجنب الاستعلامات المعقدة
- استخدم pagination للبيانات الكبيرة

### 2. تحسين الشبكة
- استخدم ضغط gzip
- قم بتخزين الأصول مؤقتاً
- استخدم CDN للملفات الثابتة

### 3. تحسين الكود
- تجنب memory leaks
- استخدم async/await بدلاً من callbacks
- قم بتنظيف event listeners

### 4. مراقبة الأداء
- استخدم أدوات profiling
- راقب استهلاك الذاكرة
- قس أوقات الاستجابة

هذه الإرشادات ستساعد في الحصول على أفضل أداء من VSCodeX.
